#!/usr/bin/env python3
"""
数据驱动的粒子初始化器模块

该模块负责：
1. 基于21个实际温度样本创建种子粒子
2. 通过变异和扰动生成多样化的初始种群
3. 确保初始粒子具有真实的业务特征
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional
import logging
import yaml
import random
from scipy import interpolate
import os

logger = logging.getLogger(__name__)


class DataDrivenInitializer:
    """数据驱动的粒子初始化器"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化数据驱动初始化器
        
        Args:
            config_path: 配置文件路径
        """
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        self.data_config = self.config['data']
        self.pso_config = self.config['pso']
        self.temp_config = self.pso_config['temperature_sequence']
        
        # 数据目录
        self.data_dir = self.data_config['data_dir']
        
        # PSO参数
        self.control_points = self.temp_config['control_points']
        self.sequence_length = self.temp_config['sequence_length']
        self.min_temp = self.temp_config['min_temperature']
        self.max_temp = self.temp_config['max_temperature']
        
        # 变长序列参数
        self.variable_length_config = self.temp_config.get('variable_length', {})
        self.variable_length_enabled = self.variable_length_config.get('enable', False)
        
        if self.variable_length_enabled:
            self.min_length = self.variable_length_config['min_length']
            self.max_length = self.variable_length_config['max_length']
            self.default_length = self.variable_length_config['default_length']
        
        # 存储实际数据
        self.real_temperature_sequences = {}
        self.seed_control_points = []
        
        # 初始化参数
        self.mutation_strength = 0.1      # 变异强度
        self.perturbation_ratio = 0.3     # 扰动比例
        self.diversity_factor = 0.2       # 多样性因子
        
        logger.info("数据驱动初始化器初始化完成")
    
    def load_real_temperature_data(self) -> Dict[int, np.ndarray]:
        """
        加载所有21个实际温度序列数据
        
        Returns:
            实际温度序列字典
        """
        logger.info("加载实际温度序列数据...")
        sequences = {}
        
        for sample_id in range(1, 22):  # 样本1-21
            sample_file = os.path.join(
                self.data_dir, 
                self.data_config['sample_file_pattern'].format(sample_id)
            )
            
            try:
                # 读取Excel文件
                df = pd.read_excel(sample_file, header=None)
                temperature_sequence = df.iloc[:, 0].values
                
                # 数据清洗：移除NaN值
                temperature_sequence = temperature_sequence[~np.isnan(temperature_sequence)]
                
                if len(temperature_sequence) > 0:
                    sequences[sample_id] = temperature_sequence
                    logger.info(f"加载样本 {sample_id}，序列长度: {len(temperature_sequence):,}")
                else:
                    logger.warning(f"样本 {sample_id} 数据为空")
                    
            except Exception as e:
                logger.error(f"加载样本 {sample_id} 失败: {e}")
                continue
        
        self.real_temperature_sequences = sequences
        logger.info(f"成功加载了 {len(sequences)} 个实际温度序列")
        return sequences
    
    def extract_control_points_from_real_data(self) -> List[np.ndarray]:
        """
        从实际数据中提取控制点
        
        Returns:
            控制点列表
        """
        logger.info("从实际数据中提取控制点...")
        
        if not self.real_temperature_sequences:
            self.load_real_temperature_data()
        
        control_points_list = []
        
        for sample_id, sequence in self.real_temperature_sequences.items():
            try:
                if self.variable_length_enabled:
                    # 变长模式：提取控制点 + 长度参数
                    control_points = self._extract_control_points_variable_length(sequence)
                else:
                    # 固定长度模式：提取控制点
                    control_points = self._extract_control_points_fixed_length(sequence)
                
                control_points_list.append(control_points)
                logger.debug(f"样本 {sample_id} 控制点提取完成，维度: {len(control_points)}")
                
            except Exception as e:
                logger.warning(f"样本 {sample_id} 控制点提取失败: {e}")
                continue
        
        self.seed_control_points = control_points_list
        logger.info(f"成功提取了 {len(control_points_list)} 组种子控制点")
        return control_points_list
    
    def _extract_control_points_fixed_length(self, sequence: np.ndarray) -> np.ndarray:
        """
        从固定长度序列中提取控制点
        
        Args:
            sequence: 温度序列
            
        Returns:
            控制点数组
        """
        # 重采样到目标长度
        if len(sequence) != self.sequence_length:
            indices = np.linspace(0, len(sequence) - 1, self.sequence_length).astype(int)
            resampled_sequence = sequence[indices]
        else:
            resampled_sequence = sequence
        
        # 从重采样序列中提取控制点
        control_indices = np.linspace(0, len(resampled_sequence) - 1, self.control_points).astype(int)
        control_points = resampled_sequence[control_indices]
        
        return control_points
    
    def _extract_control_points_variable_length(self, sequence: np.ndarray) -> np.ndarray:
        """
        从变长序列中提取控制点（包含长度参数）
        
        Args:
            sequence: 温度序列
            
        Returns:
            控制点数组（第一个元素是长度参数，其余是温度控制点）
        """
        # 计算长度参数
        seq_length = len(sequence)
        length_param = (seq_length - self.min_length) / (self.max_length - self.min_length)
        length_param = np.clip(length_param, 0, 1)
        
        # 提取温度控制点
        control_indices = np.linspace(0, len(sequence) - 1, self.control_points).astype(int)
        temp_control_points = sequence[control_indices]
        
        # 组合长度参数和温度控制点
        control_points = np.concatenate([[length_param], temp_control_points])
        
        return control_points
    
    def generate_mutated_particles(self, seed_control_points: np.ndarray, 
                                 num_mutations: int = 5) -> List[np.ndarray]:
        """
        基于种子控制点生成变异粒子
        
        Args:
            seed_control_points: 种子控制点
            num_mutations: 变异粒子数量
            
        Returns:
            变异粒子列表
        """
        mutated_particles = []
        
        for _ in range(num_mutations):
            # 复制种子
            mutated = seed_control_points.copy()
            
            if self.variable_length_enabled:
                # 变长模式：分别处理长度参数和温度控制点
                # 长度参数变异（较小幅度）
                length_mutation = np.random.normal(0, 0.05)
                mutated[0] = np.clip(mutated[0] + length_mutation, 0, 1)
                
                # 温度控制点变异
                temp_mutations = np.random.normal(0, self.mutation_strength * 
                                                (self.max_temp - self.min_temp), 
                                                len(mutated) - 1)
                mutated[1:] += temp_mutations
                mutated[1:] = np.clip(mutated[1:], self.min_temp, self.max_temp)
            else:
                # 固定长度模式：所有控制点变异
                mutations = np.random.normal(0, self.mutation_strength * 
                                           (self.max_temp - self.min_temp), 
                                           len(mutated))
                mutated += mutations
                mutated = np.clip(mutated, self.min_temp, self.max_temp)
            
            mutated_particles.append(mutated)
        
        return mutated_particles
    
    def generate_perturbed_particles(self, seed_control_points: np.ndarray,
                                   num_perturbations: int = 3) -> List[np.ndarray]:
        """
        基于种子控制点生成扰动粒子
        
        Args:
            seed_control_points: 种子控制点
            num_perturbations: 扰动粒子数量
            
        Returns:
            扰动粒子列表
        """
        perturbed_particles = []
        
        for _ in range(num_perturbations):
            perturbed = seed_control_points.copy()
            
            if self.variable_length_enabled:
                # 变长模式：选择部分控制点进行扰动
                num_points_to_perturb = max(1, int(len(perturbed[1:]) * self.perturbation_ratio))
                indices_to_perturb = np.random.choice(range(1, len(perturbed)), 
                                                    num_points_to_perturb, replace=False)
                
                for idx in indices_to_perturb:
                    perturbation = np.random.uniform(-self.diversity_factor * (self.max_temp - self.min_temp),
                                                   self.diversity_factor * (self.max_temp - self.min_temp))
                    perturbed[idx] += perturbation
                    perturbed[idx] = np.clip(perturbed[idx], self.min_temp, self.max_temp)
            else:
                # 固定长度模式：选择部分控制点进行扰动
                num_points_to_perturb = max(1, int(len(perturbed) * self.perturbation_ratio))
                indices_to_perturb = np.random.choice(len(perturbed), num_points_to_perturb, replace=False)
                
                for idx in indices_to_perturb:
                    perturbation = np.random.uniform(-self.diversity_factor * (self.max_temp - self.min_temp),
                                                   self.diversity_factor * (self.max_temp - self.min_temp))
                    perturbed[idx] += perturbation
                    perturbed[idx] = np.clip(perturbed[idx], self.min_temp, self.max_temp)
            
            perturbed_particles.append(perturbed)

        return perturbed_particles

    def generate_data_driven_swarm(self, swarm_size: int) -> List[np.ndarray]:
        """
        生成数据驱动的粒子群

        Args:
            swarm_size: 粒子群大小

        Returns:
            粒子位置列表
        """
        logger.info(f"生成数据驱动的粒子群，大小: {swarm_size}")

        # 确保有种子控制点
        if not self.seed_control_points:
            self.extract_control_points_from_real_data()

        if not self.seed_control_points:
            raise ValueError("无法提取种子控制点，请检查数据")

        swarm_particles = []

        # 计算每个种子应该生成多少个粒子
        num_seeds = len(self.seed_control_points)
        particles_per_seed = swarm_size // num_seeds
        remaining_particles = swarm_size % num_seeds

        logger.info(f"使用 {num_seeds} 个种子，每个种子生成 {particles_per_seed} 个粒子")

        for i, seed_control_points in enumerate(self.seed_control_points):
            # 添加原始种子粒子
            swarm_particles.append(seed_control_points.copy())

            # 计算当前种子需要生成的粒子数
            current_particles_count = particles_per_seed
            if i < remaining_particles:
                current_particles_count += 1

            # 生成变异粒子
            mutations_per_seed = max(1, current_particles_count // 2)
            mutated_particles = self.generate_mutated_particles(
                seed_control_points, mutations_per_seed
            )
            swarm_particles.extend(mutated_particles)

            # 生成扰动粒子
            perturbations_per_seed = current_particles_count - mutations_per_seed
            if perturbations_per_seed > 0:
                perturbed_particles = self.generate_perturbed_particles(
                    seed_control_points, perturbations_per_seed
                )
                swarm_particles.extend(perturbed_particles)

        # 如果粒子数量不够，随机选择种子进行额外生成
        while len(swarm_particles) < swarm_size:
            random_seed = random.choice(self.seed_control_points)
            extra_particle = self.generate_mutated_particles(random_seed, 1)[0]
            swarm_particles.append(extra_particle)

        # 如果粒子数量过多，随机移除
        if len(swarm_particles) > swarm_size:
            swarm_particles = random.sample(swarm_particles, swarm_size)

        logger.info(f"成功生成 {len(swarm_particles)} 个数据驱动的粒子")
        return swarm_particles

    def validate_particle(self, particle: np.ndarray) -> bool:
        """
        验证粒子的有效性

        Args:
            particle: 粒子位置

        Returns:
            是否有效
        """
        try:
            if self.variable_length_enabled:
                # 变长模式验证
                if len(particle) != self.control_points + 1:
                    return False

                # 长度参数验证
                if not (0 <= particle[0] <= 1):
                    return False

                # 温度控制点验证
                temp_points = particle[1:]
                if not np.all((temp_points >= self.min_temp) & (temp_points <= self.max_temp)):
                    return False
            else:
                # 固定长度模式验证
                if len(particle) != self.control_points:
                    return False

                # 温度控制点验证
                if not np.all((particle >= self.min_temp) & (particle <= self.max_temp)):
                    return False

            return True

        except Exception as e:
            logger.warning(f"粒子验证失败: {e}")
            return False

    def get_initialization_statistics(self) -> Dict:
        """
        获取初始化统计信息

        Returns:
            统计信息字典
        """
        if not self.seed_control_points:
            return {}

        stats = {
            'num_seed_particles': len(self.seed_control_points),
            'control_points_dimension': self.control_points,
            'variable_length_enabled': self.variable_length_enabled,
            'mutation_strength': self.mutation_strength,
            'perturbation_ratio': self.perturbation_ratio,
            'diversity_factor': self.diversity_factor
        }

        if self.variable_length_enabled:
            stats['particle_dimension'] = self.control_points + 1
            stats['length_range'] = [self.min_length, self.max_length]
        else:
            stats['particle_dimension'] = self.control_points
            stats['sequence_length'] = self.sequence_length

        # 种子粒子统计
        if self.seed_control_points:
            seed_array = np.array(self.seed_control_points)

            if self.variable_length_enabled:
                # 长度参数统计
                length_params = seed_array[:, 0]
                stats['seed_length_params'] = {
                    'mean': np.mean(length_params),
                    'std': np.std(length_params),
                    'min': np.min(length_params),
                    'max': np.max(length_params)
                }

                # 温度控制点统计
                temp_points = seed_array[:, 1:]
                stats['seed_temp_points'] = {
                    'mean': np.mean(temp_points),
                    'std': np.std(temp_points),
                    'min': np.min(temp_points),
                    'max': np.max(temp_points)
                }
            else:
                # 固定长度模式统计
                stats['seed_control_points'] = {
                    'mean': np.mean(seed_array),
                    'std': np.std(seed_array),
                    'min': np.min(seed_array),
                    'max': np.max(seed_array)
                }

        return stats

    def set_initialization_parameters(self, mutation_strength: float = None,
                                    perturbation_ratio: float = None,
                                    diversity_factor: float = None):
        """
        设置初始化参数

        Args:
            mutation_strength: 变异强度
            perturbation_ratio: 扰动比例
            diversity_factor: 多样性因子
        """
        if mutation_strength is not None:
            self.mutation_strength = mutation_strength
            logger.info(f"变异强度设置为: {mutation_strength}")

        if perturbation_ratio is not None:
            self.perturbation_ratio = perturbation_ratio
            logger.info(f"扰动比例设置为: {perturbation_ratio}")

        if diversity_factor is not None:
            self.diversity_factor = diversity_factor
            logger.info(f"多样性因子设置为: {diversity_factor}")


def main():
    """测试数据驱动初始化器"""
    initializer = DataDrivenInitializer()

    try:
        # 加载实际数据
        sequences = initializer.load_real_temperature_data()
        print(f"加载了 {len(sequences)} 个实际温度序列")

        # 提取种子控制点
        seed_points = initializer.extract_control_points_from_real_data()
        print(f"提取了 {len(seed_points)} 组种子控制点")

        # 生成数据驱动的粒子群
        swarm_size = 30
        swarm = initializer.generate_data_driven_swarm(swarm_size)
        print(f"生成了 {len(swarm)} 个数据驱动的粒子")

        # 验证粒子
        valid_count = sum(1 for particle in swarm if initializer.validate_particle(particle))
        print(f"有效粒子数量: {valid_count}/{len(swarm)}")

        # 获取统计信息
        stats = initializer.get_initialization_statistics()
        print("初始化统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value}")

        print("数据驱动初始化器测试完成！")

    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
