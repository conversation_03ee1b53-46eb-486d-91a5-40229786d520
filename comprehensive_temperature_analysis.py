#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
酯化反应真实温度序列数据综合分析脚本
分析data/Esterification/目录下的所有真实温度数据文件
并与模拟温度序列进行对比分析
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import glob
import os
from pathlib import Path
from scipy import stats
from scipy.signal import savgol_filter
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class TemperatureAnalyzer:
    def __init__(self):
        self.real_data_dir = "data/Esterification/"
        self.simulated_file = "results/advanced_temperature_sequence_20250724_212347.csv"
        self.output_dir = "results/"
        self.real_data = {}
        self.simulated_data = None
        
    def load_real_data(self):
        """加载所有真实温度数据文件"""
        print("=== 加载真实温度数据文件 ===")
        
        # 获取所有Sample文件
        sample_files = glob.glob(os.path.join(self.real_data_dir, "Sample_*.xlsx"))
        sample_files.sort(key=lambda x: int(x.split('_')[-1].split('.')[0]))
        
        print(f"找到 {len(sample_files)} 个Sample文件")
        
        for file_path in sample_files:
            file_name = os.path.basename(file_path)
            sample_num = file_name.replace('Sample_', '').replace('.xlsx', '')
            
            try:
                # 读取Excel文件
                df = pd.read_excel(file_path, sheet_name=0)
                
                # 清理列名
                df.columns = [col.strip().replace('\t', '') for col in df.columns]
                
                # 转换为数值类型
                for col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                
                # 获取第一列数据（假设是温度数据）
                if len(df.columns) > 0:
                    temp_data = df.iloc[:, 0].dropna()
                    if len(temp_data) > 0:
                        self.real_data[f"Sample_{sample_num}"] = temp_data.values
                        print(f"  {file_name}: {len(temp_data)} 个数据点, 范围: {temp_data.min():.2f} - {temp_data.max():.2f}")
                    else:
                        print(f"  {file_name}: 无有效数据")
                else:
                    print(f"  {file_name}: 无数据列")
                    
            except Exception as e:
                print(f"  {file_name}: 读取失败 - {e}")
        
        print(f"\n成功加载 {len(self.real_data)} 个真实数据文件")
        return len(self.real_data) > 0
    
    def load_simulated_data(self):
        """加载模拟温度数据"""
        print("\n=== 加载模拟温度数据 ===")
        
        try:
            df = pd.read_csv(self.simulated_file)
            self.simulated_data = df['温度(°C)'].values
            print(f"模拟数据: {len(self.simulated_data)} 个数据点")
            print(f"温度范围: {self.simulated_data.min():.2f} - {self.simulated_data.max():.2f}")
            return True
        except Exception as e:
            print(f"加载模拟数据失败: {e}")
            return False
    
    def analyze_temperature_trends(self):
        """分析温度变化趋势"""
        print("\n=== 分析温度变化趋势 ===")
        
        analysis_results = {}
        
        for sample_name, temp_data in self.real_data.items():
            # 计算基本统计信息
            n_points = len(temp_data)
            
            # 分段分析：初期(前1/3)、中期(中1/3)、后期(后1/3)
            early_idx = n_points // 3
            mid_idx = 2 * n_points // 3
            
            early_data = temp_data[:early_idx]
            mid_data = temp_data[early_idx:mid_idx]
            late_data = temp_data[mid_idx:]
            
            # 计算各阶段的线性趋势
            def calc_trend(data, start_idx=0):
                if len(data) < 2:
                    return 0, 0, 0
                x = np.arange(len(data)) + start_idx
                slope, intercept, r_value, p_value, std_err = stats.linregress(x, data)
                return slope, r_value, std_err
            
            early_slope, early_r, early_err = calc_trend(early_data, 0)
            mid_slope, mid_r, mid_err = calc_trend(mid_data, early_idx)
            late_slope, late_r, late_err = calc_trend(late_data, mid_idx)
            
            # 计算变化率
            diff_data = np.diff(temp_data)
            avg_change_rate = np.mean(diff_data)
            std_change_rate = np.std(diff_data)
            
            # 检测平衡状态（后期数据的标准差）
            late_stability = np.std(late_data)
            
            analysis_results[sample_name] = {
                'n_points': n_points,
                'temp_range': (temp_data.min(), temp_data.max()),
                'mean_temp': np.mean(temp_data),
                'early_slope': early_slope,
                'mid_slope': mid_slope,
                'late_slope': late_slope,
                'early_r': early_r,
                'mid_r': mid_r,
                'late_r': late_r,
                'avg_change_rate': avg_change_rate,
                'std_change_rate': std_change_rate,
                'late_stability': late_stability,
                'final_temp': temp_data[-1],
                'initial_temp': temp_data[0]
            }
        
        return analysis_results
    
    def plot_all_real_data(self):
        """绘制所有真实数据的温度曲线"""
        print("\n=== 绘制所有真实数据曲线 ===")
        
        n_samples = len(self.real_data)
        if n_samples == 0:
            print("没有真实数据可绘制")
            return
        
        # 计算子图布局
        n_cols = 4
        n_rows = (n_samples + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 5*n_rows))
        if n_rows == 1:
            axes = axes.reshape(1, -1)
        
        fig.suptitle('酯化反应真实温度序列数据 - 所有样本', fontsize=16, fontweight='bold')
        
        for idx, (sample_name, temp_data) in enumerate(self.real_data.items()):
            row = idx // n_cols
            col = idx % n_cols
            ax = axes[row, col]
            
            # 绘制温度曲线
            time_points = np.arange(len(temp_data))
            ax.plot(time_points, temp_data, 'b-', linewidth=1, alpha=0.8)
            
            # 添加趋势线
            z = np.polyfit(time_points, temp_data, 1)
            p = np.poly1d(z)
            ax.plot(time_points, p(time_points), "r--", alpha=0.8, linewidth=2)
            
            ax.set_title(f'{sample_name}', fontsize=10, fontweight='bold')
            ax.set_xlabel('时间点')
            ax.set_ylabel('温度')
            ax.grid(True, alpha=0.3)
            
            # 添加统计信息
            stats_text = f'范围: {temp_data.min():.1f}-{temp_data.max():.1f}\n斜率: {z[0]:.4f}'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                   verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
                   fontsize=8)
        
        # 隐藏多余的子图
        for idx in range(n_samples, n_rows * n_cols):
            row = idx // n_cols
            col = idx % n_cols
            axes[row, col].set_visible(False)
        
        plt.tight_layout()
        
        # 保存图片
        output_file = os.path.join(self.output_dir, "all_real_temperature_data.png")
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"所有真实数据图表已保存至: {output_file}")
        plt.show()
    
    def plot_comparison_analysis(self):
        """绘制真实数据与模拟数据的对比分析"""
        print("\n=== 绘制对比分析图 ===")
        
        if self.simulated_data is None:
            print("没有模拟数据进行对比")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('真实温度数据 vs 模拟温度数据 对比分析', fontsize=16, fontweight='bold')
        
        # 1. 多条真实数据曲线 vs 模拟数据
        ax1 = axes[0, 0]
        
        # 绘制真实数据（选择几个代表性样本）
        sample_names = list(self.real_data.keys())[:5]  # 选择前5个样本
        colors = ['blue', 'green', 'orange', 'purple', 'brown']
        
        for i, sample_name in enumerate(sample_names):
            temp_data = self.real_data[sample_name]
            time_points = np.arange(len(temp_data))
            ax1.plot(time_points, temp_data, color=colors[i], linewidth=2, alpha=0.7, label=f'{sample_name}')
        
        # 绘制模拟数据（调整长度以匹配）
        sim_time = np.arange(len(self.simulated_data))
        ax1.plot(sim_time, self.simulated_data, 'red', linewidth=3, alpha=0.8, label='模拟数据', linestyle='--')
        
        ax1.set_title('温度曲线对比', fontsize=12, fontweight='bold')
        ax1.set_xlabel('时间点')
        ax1.set_ylabel('温度 (°C)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 平均真实数据 vs 模拟数据
        ax2 = axes[0, 1]
        
        # 计算真实数据的平均曲线（需要统一长度）
        min_length = min([len(data) for data in self.real_data.values()])
        real_data_matrix = np.array([data[:min_length] for data in self.real_data.values()])
        mean_real_data = np.mean(real_data_matrix, axis=0)
        std_real_data = np.std(real_data_matrix, axis=0)
        
        time_real = np.arange(min_length)
        ax2.plot(time_real, mean_real_data, 'blue', linewidth=3, label='真实数据均值')
        ax2.fill_between(time_real, mean_real_data - std_real_data, mean_real_data + std_real_data, 
                        alpha=0.3, color='blue', label='真实数据±1σ')
        
        # 模拟数据（截取相同长度）
        sim_data_truncated = self.simulated_data[:min_length]
        ax2.plot(time_real, sim_data_truncated, 'red', linewidth=3, linestyle='--', label='模拟数据')
        
        ax2.set_title('平均温度曲线对比', fontsize=12, fontweight='bold')
        ax2.set_xlabel('时间点')
        ax2.set_ylabel('温度 (°C)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 变化率对比
        ax3 = axes[1, 0]
        
        # 真实数据变化率
        real_change_rates = [np.diff(data) for data in self.real_data.values()]
        mean_real_change = np.mean([np.mean(rates) for rates in real_change_rates])
        
        # 模拟数据变化率
        sim_change_rate = np.diff(self.simulated_data)
        mean_sim_change = np.mean(sim_change_rate)
        
        # 绘制变化率分布
        all_real_changes = np.concatenate(real_change_rates)
        ax3.hist(all_real_changes, bins=50, alpha=0.7, color='blue', density=True, label='真实数据变化率')
        ax3.hist(sim_change_rate, bins=50, alpha=0.7, color='red', density=True, label='模拟数据变化率')
        
        ax3.axvline(mean_real_change, color='blue', linestyle='-', linewidth=2, label=f'真实均值: {mean_real_change:.4f}')
        ax3.axvline(mean_sim_change, color='red', linestyle='--', linewidth=2, label=f'模拟均值: {mean_sim_change:.4f}')
        
        ax3.set_title('温度变化率分布对比', fontsize=12, fontweight='bold')
        ax3.set_xlabel('温度变化率 (°C/步)')
        ax3.set_ylabel('密度')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 后期稳定性分析
        ax4 = axes[1, 1]
        
        # 分析后期稳定性（后1/3数据的标准差）
        real_late_stds = []
        real_late_means = []
        
        for temp_data in self.real_data.values():
            late_start = 2 * len(temp_data) // 3
            late_data = temp_data[late_start:]
            real_late_stds.append(np.std(late_data))
            real_late_means.append(np.mean(late_data))
        
        # 模拟数据后期稳定性
        sim_late_start = 2 * len(self.simulated_data) // 3
        sim_late_data = self.simulated_data[sim_late_start:]
        sim_late_std = np.std(sim_late_data)
        sim_late_mean = np.mean(sim_late_data)
        
        # 绘制稳定性对比
        ax4.scatter(real_late_means, real_late_stds, color='blue', s=50, alpha=0.7, label='真实数据')
        ax4.scatter([sim_late_mean], [sim_late_std], color='red', s=100, marker='x', label='模拟数据')
        
        ax4.set_title('后期稳定性分析', fontsize=12, fontweight='bold')
        ax4.set_xlabel('后期平均温度 (°C)')
        ax4.set_ylabel('后期温度标准差 (°C)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        output_file = os.path.join(self.output_dir, "temperature_comparison_analysis.png")
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"对比分析图表已保存至: {output_file}")
        plt.show()
    
    def generate_statistical_report(self, analysis_results):
        """生成统计分析报告"""
        print("\n=== 生成统计分析报告 ===")
        
        # 收集所有统计数据
        all_slopes = []
        all_late_slopes = []
        all_stabilities = []
        all_change_rates = []
        all_final_temps = []
        all_temp_ranges = []
        
        for result in analysis_results.values():
            all_slopes.append(result['early_slope'])
            all_late_slopes.append(result['late_slope'])
            all_stabilities.append(result['late_stability'])
            all_change_rates.append(result['avg_change_rate'])
            all_final_temps.append(result['final_temp'])
            all_temp_ranges.append(result['temp_range'][1] - result['temp_range'][0])
        
        # 生成报告
        report = f"""
# 酯化反应真实温度序列数据分析报告

## 数据概览
- 分析文件数量: {len(self.real_data)}
- 总数据点数: {sum([len(data) for data in self.real_data.values()]):,}

## 温度特征统计

### 初期温度变化特征
- 平均斜率: {np.mean(all_slopes):.6f} ± {np.std(all_slopes):.6f}
- 斜率范围: {np.min(all_slopes):.6f} ~ {np.max(all_slopes):.6f}

### 后期温度变化特征  
- 平均斜率: {np.mean(all_late_slopes):.6f} ± {np.std(all_late_slopes):.6f}
- 斜率范围: {np.min(all_late_slopes):.6f} ~ {np.max(all_late_slopes):.6f}

### 动态平衡特征
- 后期稳定性(标准差): {np.mean(all_stabilities):.3f} ± {np.std(all_stabilities):.3f}
- 稳定性范围: {np.min(all_stabilities):.3f} ~ {np.max(all_stabilities):.3f}

### 整体变化特征
- 平均变化率: {np.mean(all_change_rates):.6f} ± {np.std(all_change_rates):.6f}
- 最终温度: {np.mean(all_final_temps):.2f} ± {np.std(all_final_temps):.2f}
- 温度范围: {np.mean(all_temp_ranges):.2f} ± {np.std(all_temp_ranges):.2f}

## 与模拟数据对比分析
"""
        
        if self.simulated_data is not None:
            sim_change_rate = np.mean(np.diff(self.simulated_data))
            sim_late_start = 2 * len(self.simulated_data) // 3
            sim_late_slope = stats.linregress(np.arange(len(self.simulated_data[sim_late_start:])), 
                                            self.simulated_data[sim_late_start:])[0]
            
            report += f"""
### 关键差异点
1. **后期趋势差异**:
   - 真实数据后期平均斜率: {np.mean(all_late_slopes):.6f}
   - 模拟数据后期斜率: {sim_late_slope:.6f}
   - 差异倍数: {abs(sim_late_slope / np.mean(all_late_slopes)):.2f}倍

2. **变化率差异**:
   - 真实数据平均变化率: {np.mean(all_change_rates):.6f}
   - 模拟数据平均变化率: {sim_change_rate:.6f}
   - 差异: {abs(sim_change_rate - np.mean(all_change_rates)):.6f}

3. **稳定性差异**:
   - 真实数据后期稳定性: {np.mean(all_stabilities):.3f}
   - 模拟数据后期稳定性: {np.std(self.simulated_data[sim_late_start:]):.3f}
"""
        
        report += f"""
## 改进建议

### 针对模拟算法的建议:
1. **后期趋势修正**: 真实数据显示后期温度趋于稳定，建议在模拟算法中加入平衡态约束
2. **变化率控制**: 调整温度变化的步长和幅度，使其更接近真实数据的变化模式
3. **动态平衡机制**: 引入反馈控制机制，当温度达到一定阈值后减缓上升速度

### 典型真实温度序列特征:
1. **三阶段特征**: 初期快速上升 → 中期稳定上升 → 后期动态平衡
2. **收敛特性**: 大多数样本在后期表现出收敛到稳定温度的趋势
3. **变化平稳**: 温度变化相对平稳，无剧烈波动

## 详细样本分析
"""
        
        # 添加每个样本的详细信息
        for sample_name, result in analysis_results.items():
            report += f"""
### {sample_name}
- 数据点数: {result['n_points']:,}
- 温度范围: {result['temp_range'][0]:.2f} - {result['temp_range'][1]:.2f} °C
- 初期斜率: {result['early_slope']:.6f} (R²={result['early_r']**2:.3f})
- 中期斜率: {result['mid_slope']:.6f} (R²={result['mid_r']**2:.3f})  
- 后期斜率: {result['late_slope']:.6f} (R²={result['late_r']**2:.3f})
- 后期稳定性: {result['late_stability']:.3f}
- 平均变化率: {result['avg_change_rate']:.6f}
"""
        
        # 保存报告
        report_file = os.path.join(self.output_dir, "temperature_analysis_report.md")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"分析报告已保存至: {report_file}")
        print("\n" + "="*50)
        print(report)
        
        return report

def main():
    """主函数"""
    print("酯化反应温度序列数据综合分析")
    print("="*50)
    
    # 创建分析器
    analyzer = TemperatureAnalyzer()
    
    # 加载数据
    if not analyzer.load_real_data():
        print("无法加载真实数据，程序退出")
        return
    
    if not analyzer.load_simulated_data():
        print("无法加载模拟数据，将只分析真实数据")
    
    # 分析温度趋势
    analysis_results = analyzer.analyze_temperature_trends()
    
    # 绘制图表
    analyzer.plot_all_real_data()
    analyzer.plot_comparison_analysis()
    
    # 生成报告
    analyzer.generate_statistical_report(analysis_results)
    
    print("\n分析完成！所有结果已保存到results目录。")

if __name__ == "__main__":
    main()
