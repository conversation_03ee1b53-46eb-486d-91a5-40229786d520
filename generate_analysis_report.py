#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成温度序列分析报告
"""

import pandas as pd
import numpy as np
import glob
import os
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def load_and_analyze_data():
    """加载并分析所有数据"""
    
    # 加载真实数据
    real_data_dir = "data/Esterification/"
    sample_files = glob.glob(os.path.join(real_data_dir, "Sample_*.xlsx"))
    sample_files.sort(key=lambda x: int(x.split('_')[-1].split('.')[0]))
    
    real_data = {}
    print("加载真实数据...")
    
    for file_path in sample_files:
        file_name = os.path.basename(file_path)
        sample_num = file_name.replace('Sample_', '').replace('.xlsx', '')
        
        try:
            df = pd.read_excel(file_path, sheet_name=0)
            df.columns = [col.strip().replace('\t', '') for col in df.columns]
            
            for col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            if len(df.columns) > 0:
                temp_data = df.iloc[:, 0].dropna()
                if len(temp_data) > 0:
                    real_data[f"Sample_{sample_num}"] = temp_data.values
                    print(f"  {file_name}: {len(temp_data)} 个数据点")
        except Exception as e:
            print(f"  {file_name}: 读取失败 - {e}")
    
    # 加载模拟数据
    print("\n加载模拟数据...")
    simulated_file = "results/advanced_temperature_sequence_20250724_212347.csv"
    try:
        df_sim = pd.read_csv(simulated_file)
        simulated_data = df_sim['温度(°C)'].values
        print(f"  模拟数据: {len(simulated_data)} 个数据点")
    except Exception as e:
        print(f"  加载模拟数据失败: {e}")
        simulated_data = None
    
    return real_data, simulated_data

def analyze_temperature_trends(real_data):
    """分析温度变化趋势"""
    
    analysis_results = {}
    
    for sample_name, temp_data in real_data.items():
        n_points = len(temp_data)
        
        # 分段分析
        early_idx = n_points // 3
        mid_idx = 2 * n_points // 3
        
        early_data = temp_data[:early_idx]
        mid_data = temp_data[early_idx:mid_idx]
        late_data = temp_data[mid_idx:]
        
        # 计算趋势
        def calc_trend(data, start_idx=0):
            if len(data) < 2:
                return 0, 0, 0
            x = np.arange(len(data)) + start_idx
            slope, intercept, r_value, p_value, std_err = stats.linregress(x, data)
            return slope, r_value, std_err
        
        early_slope, early_r, early_err = calc_trend(early_data, 0)
        mid_slope, mid_r, mid_err = calc_trend(mid_data, early_idx)
        late_slope, late_r, late_err = calc_trend(late_data, mid_idx)
        
        # 计算变化率
        diff_data = np.diff(temp_data)
        avg_change_rate = np.mean(diff_data)
        std_change_rate = np.std(diff_data)
        
        # 后期稳定性
        late_stability = np.std(late_data)
        
        analysis_results[sample_name] = {
            'n_points': n_points,
            'temp_range': (temp_data.min(), temp_data.max()),
            'mean_temp': np.mean(temp_data),
            'early_slope': early_slope,
            'mid_slope': mid_slope,
            'late_slope': late_slope,
            'early_r': early_r,
            'mid_r': mid_r,
            'late_r': late_r,
            'avg_change_rate': avg_change_rate,
            'std_change_rate': std_change_rate,
            'late_stability': late_stability,
            'final_temp': temp_data[-1],
            'initial_temp': temp_data[0]
        }
    
    return analysis_results

def generate_report(real_data, simulated_data, analysis_results):
    """生成分析报告"""
    
    # 收集统计数据
    all_slopes = [r['early_slope'] for r in analysis_results.values()]
    all_late_slopes = [r['late_slope'] for r in analysis_results.values()]
    all_stabilities = [r['late_stability'] for r in analysis_results.values()]
    all_change_rates = [r['avg_change_rate'] for r in analysis_results.values()]
    all_final_temps = [r['final_temp'] for r in analysis_results.values()]
    all_temp_ranges = [r['temp_range'][1] - r['temp_range'][0] for r in analysis_results.values()]
    
    # 生成报告
    report = f"""# 酯化反应真实温度序列数据分析报告

## 数据概览
- 分析文件数量: {len(real_data)}
- 总数据点数: {sum([len(data) for data in real_data.values()]):,}

## 温度特征统计

### 初期温度变化特征
- 平均斜率: {np.mean(all_slopes):.6f} ± {np.std(all_slopes):.6f}
- 斜率范围: {np.min(all_slopes):.6f} ~ {np.max(all_slopes):.6f}

### 后期温度变化特征  
- 平均斜率: {np.mean(all_late_slopes):.6f} ± {np.std(all_late_slopes):.6f}
- 斜率范围: {np.min(all_late_slopes):.6f} ~ {np.max(all_late_slopes):.6f}

### 动态平衡特征
- 后期稳定性(标准差): {np.mean(all_stabilities):.3f} ± {np.std(all_stabilities):.3f}
- 稳定性范围: {np.min(all_stabilities):.3f} ~ {np.max(all_stabilities):.3f}

### 整体变化特征
- 平均变化率: {np.mean(all_change_rates):.6f} ± {np.std(all_change_rates):.6f}
- 最终温度: {np.mean(all_final_temps):.2f} ± {np.std(all_final_temps):.2f}
- 温度范围: {np.mean(all_temp_ranges):.2f} ± {np.std(all_temp_ranges):.2f}

## 与模拟数据对比分析
"""
    
    if simulated_data is not None:
        sim_change_rate = np.mean(np.diff(simulated_data))
        sim_late_start = 2 * len(simulated_data) // 3
        sim_late_slope = stats.linregress(np.arange(len(simulated_data[sim_late_start:])), 
                                        simulated_data[sim_late_start:])[0]
        
        report += f"""
### 关键差异点
1. **后期趋势差异**:
   - 真实数据后期平均斜率: {np.mean(all_late_slopes):.6f}
   - 模拟数据后期斜率: {sim_late_slope:.6f}
   - 差异倍数: {abs(sim_late_slope / np.mean(all_late_slopes)) if np.mean(all_late_slopes) != 0 else float('inf'):.2f}倍

2. **变化率差异**:
   - 真实数据平均变化率: {np.mean(all_change_rates):.6f}
   - 模拟数据平均变化率: {sim_change_rate:.6f}
   - 差异: {abs(sim_change_rate - np.mean(all_change_rates)):.6f}

3. **稳定性差异**:
   - 真实数据后期稳定性: {np.mean(all_stabilities):.3f}
   - 模拟数据后期稳定性: {np.std(simulated_data[sim_late_start:]):.3f}
"""
    
    report += f"""
## 改进建议

### 针对模拟算法的建议:
1. **后期趋势修正**: 真实数据显示后期温度趋于稳定，建议在模拟算法中加入平衡态约束
2. **变化率控制**: 调整温度变化的步长和幅度，使其更接近真实数据的变化模式
3. **动态平衡机制**: 引入反馈控制机制，当温度达到一定阈值后减缓上升速度

### 典型真实温度序列特征:
1. **三阶段特征**: 初期快速上升 → 中期稳定上升 → 后期动态平衡
2. **收敛特性**: 大多数样本在后期表现出收敛到稳定温度的趋势
3. **变化平稳**: 温度变化相对平稳，无剧烈波动

## 详细样本分析
"""
    
    # 添加每个样本的详细信息
    for sample_name, result in analysis_results.items():
        report += f"""
### {sample_name}
- 数据点数: {result['n_points']:,}
- 温度范围: {result['temp_range'][0]:.2f} - {result['temp_range'][1]:.2f} °C
- 初期斜率: {result['early_slope']:.6f} (R²={result['early_r']**2:.3f})
- 中期斜率: {result['mid_slope']:.6f} (R²={result['mid_r']**2:.3f})  
- 后期斜率: {result['late_slope']:.6f} (R²={result['late_r']**2:.3f})
- 后期稳定性: {result['late_stability']:.3f}
- 平均变化率: {result['avg_change_rate']:.6f}
"""
    
    return report

def main():
    """主函数"""
    print("生成温度序列分析报告...")
    
    # 加载数据
    real_data, simulated_data = load_and_analyze_data()
    
    if not real_data:
        print("无法加载真实数据")
        return
    
    # 分析数据
    print("\n分析温度趋势...")
    analysis_results = analyze_temperature_trends(real_data)
    
    # 生成报告
    print("\n生成报告...")
    report = generate_report(real_data, simulated_data, analysis_results)
    
    # 保存报告
    report_file = "results/temperature_analysis_report.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n分析报告已保存至: {report_file}")
    print("\n" + "="*50)
    print(report)

if __name__ == "__main__":
    main()
