#!/usr/bin/env python3
"""
绘制results目录中两个CSV温度序列文件的曲线图
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def load_temperature_data(csv_path):
    """加载温度序列数据"""
    try:
        df = pd.read_csv(csv_path, encoding='utf-8')
        return df
    except Exception as e:
        print(f"加载文件失败 {csv_path}: {e}")
        return None

def plot_temperature_sequences():
    """绘制温度序列曲线图"""
    
    # 文件路径
    file1 = "results/advanced_temperature_sequence_20250724_153803.csv"
    file2 = "results/advanced_temperature_sequence_20250724_172214.csv"
    
    # 检查文件是否存在
    if not os.path.exists(file1):
        print(f"文件不存在: {file1}")
        return
    if not os.path.exists(file2):
        print(f"文件不存在: {file2}")
        return
    
    # 加载数据
    print("加载温度序列数据...")
    df1 = load_temperature_data(file1)
    df2 = load_temperature_data(file2)
    
    if df1 is None or df2 is None:
        print("数据加载失败")
        return
    
    # 提取温度数据
    temp1 = df1['温度(°C)'].values
    temp2 = df2['温度(°C)'].values
    
    # 提取其他信息
    fitness1 = df1['适应度'].iloc[0]
    fitness2 = df2['适应度'].iloc[0]
    iterations1 = df1['迭代次数'].iloc[0]
    iterations2 = df2['迭代次数'].iloc[0]
    
    print(f"序列1: 长度={len(temp1):,}, 适应度={fitness1:.6f}, 迭代次数={iterations1}")
    print(f"序列2: 长度={len(temp2):,}, 适应度={fitness2:.6f}, 迭代次数={iterations2}")
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('无约束PSO温度序列优化结果对比', fontsize=16, fontweight='bold')
    
    # 1. 完整序列对比（上左）
    ax1 = axes[0, 0]
    
    # 为了对比，将较长的序列重采样到较短序列的长度
    min_length = min(len(temp1), len(temp2))
    
    if len(temp1) > min_length:
        indices1 = np.linspace(0, len(temp1)-1, min_length).astype(int)
        temp1_resampled = temp1[indices1]
    else:
        temp1_resampled = temp1
        
    if len(temp2) > min_length:
        indices2 = np.linspace(0, len(temp2)-1, min_length).astype(int)
        temp2_resampled = temp2[indices2]
    else:
        temp2_resampled = temp2
    
    time_points = np.arange(min_length)
    
    ax1.plot(time_points, temp1_resampled, 
             label=f'序列1 (适应度: {fitness1:.4f}, 迭代: {iterations1})', 
             linewidth=2, color='blue', alpha=0.8)
    ax1.plot(time_points, temp2_resampled, 
             label=f'序列2 (适应度: {fitness2:.4f}, 迭代: {iterations2})', 
             linewidth=2, color='red', alpha=0.8)
    
    ax1.set_title('完整温度序列对比')
    ax1.set_xlabel('时间点')
    ax1.set_ylabel('温度 (°C)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 前1000个点的详细对比（上右）
    ax2 = axes[0, 1]
    detail_points = min(1000, min_length)
    
    ax2.plot(time_points[:detail_points], temp1_resampled[:detail_points], 
             label=f'序列1 (前{detail_points}点)', 
             linewidth=2, color='blue', alpha=0.8)
    ax2.plot(time_points[:detail_points], temp2_resampled[:detail_points], 
             label=f'序列2 (前{detail_points}点)', 
             linewidth=2, color='red', alpha=0.8)
    
    ax2.set_title(f'前{detail_points}个时间点详细对比')
    ax2.set_xlabel('时间点')
    ax2.set_ylabel('温度 (°C)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 温度统计对比（下左）
    ax3 = axes[1, 0]
    
    stats_data = {
        '起始温度': [temp1[0], temp2[0]],
        '结束温度': [temp1[-1], temp2[-1]],
        '最高温度': [np.max(temp1), np.max(temp2)],
        '最低温度': [np.min(temp1), np.min(temp2)],
        '平均温度': [np.mean(temp1), np.mean(temp2)],
        '温度上升': [temp1[-1] - temp1[0], temp2[-1] - temp2[0]]
    }
    
    x = np.arange(len(stats_data))
    width = 0.35
    
    values1 = [stats_data[key][0] for key in stats_data.keys()]
    values2 = [stats_data[key][1] for key in stats_data.keys()]
    
    bars1 = ax3.bar(x - width/2, values1, width, label='序列1', color='blue', alpha=0.7)
    bars2 = ax3.bar(x + width/2, values2, width, label='序列2', color='red', alpha=0.7)
    
    ax3.set_title('温度统计对比')
    ax3.set_ylabel('温度 (°C)')
    ax3.set_xticks(x)
    ax3.set_xticklabels(stats_data.keys(), rotation=45, ha='right')
    ax3.legend()
    ax3.grid(True, alpha=0.3, axis='y')
    
    # 在柱状图上添加数值
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{height:.1f}', ha='center', va='bottom', fontsize=8)
    
    # 4. 温度变化率对比（下右）
    ax4 = axes[1, 1]
    
    # 计算温度变化率
    changes1 = np.diff(temp1_resampled)
    changes2 = np.diff(temp2_resampled)
    
    # 绘制变化率的移动平均
    window_size = max(1, min_length // 100)
    
    def moving_average(data, window):
        return np.convolve(data, np.ones(window)/window, mode='valid')
    
    if len(changes1) > window_size:
        smooth_changes1 = moving_average(changes1, window_size)
        smooth_changes2 = moving_average(changes2, window_size)
        change_time = np.arange(len(smooth_changes1))
        
        ax4.plot(change_time, smooth_changes1, 
                label=f'序列1变化率 (平滑窗口: {window_size})', 
                linewidth=2, color='blue', alpha=0.8)
        ax4.plot(change_time, smooth_changes2, 
                label=f'序列2变化率 (平滑窗口: {window_size})', 
                linewidth=2, color='red', alpha=0.8)
    else:
        ax4.plot(changes1, label='序列1变化率', linewidth=2, color='blue', alpha=0.8)
        ax4.plot(changes2, label='序列2变化率', linewidth=2, color='red', alpha=0.8)
    
    ax4.set_title('温度变化率对比')
    ax4.set_xlabel('时间点')
    ax4.set_ylabel('温度变化率 (°C/点)')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    ax4.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = f"results/temperature_sequences_comparison_{timestamp}.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    
    print(f"\n图表已保存: {output_path}")
    
    # 显示图表
    plt.show()
    
    # 打印详细统计信息
    print("\n" + "="*60)
    print("详细统计信息")
    print("="*60)
    
    print(f"\n序列1 (文件: {os.path.basename(file1)}):")
    print(f"  数据点数: {len(temp1):,}")
    print(f"  适应度: {fitness1:.6f}")
    print(f"  迭代次数: {iterations1}")
    print(f"  起始温度: {temp1[0]:.2f}°C")
    print(f"  结束温度: {temp1[-1]:.2f}°C")
    print(f"  温度上升: {temp1[-1] - temp1[0]:.2f}°C")
    print(f"  最高温度: {np.max(temp1):.2f}°C")
    print(f"  最低温度: {np.min(temp1):.2f}°C")
    print(f"  平均温度: {np.mean(temp1):.2f}°C")
    print(f"  温度标准差: {np.std(temp1):.2f}°C")
    
    print(f"\n序列2 (文件: {os.path.basename(file2)}):")
    print(f"  数据点数: {len(temp2):,}")
    print(f"  适应度: {fitness2:.6f}")
    print(f"  迭代次数: {iterations2}")
    print(f"  起始温度: {temp2[0]:.2f}°C")
    print(f"  结束温度: {temp2[-1]:.2f}°C")
    print(f"  温度上升: {temp2[-1] - temp2[0]:.2f}°C")
    print(f"  最高温度: {np.max(temp2):.2f}°C")
    print(f"  最低温度: {np.min(temp2):.2f}°C")
    print(f"  平均温度: {np.mean(temp2):.2f}°C")
    print(f"  温度标准差: {np.std(temp2):.2f}°C")
    
    print(f"\n对比分析:")
    print(f"  适应度差异: {abs(fitness2 - fitness1):.6f}")
    print(f"  温度上升差异: {abs((temp2[-1] - temp2[0]) - (temp1[-1] - temp1[0])):.2f}°C")
    print(f"  平均温度差异: {abs(np.mean(temp2) - np.mean(temp1)):.2f}°C")

if __name__ == "__main__":
    print("开始绘制温度序列曲线图...")
    plot_temperature_sequences()
    print("绘制完成！")
