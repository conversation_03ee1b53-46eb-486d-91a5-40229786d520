#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sample_1.xlsx 数据分析和曲线绘制脚本
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def analyze_excel_file():
    """分析Excel文件并绘制曲线图"""
    
    excel_file = "data/Esterification/Sample_1.xlsx"
    
    try:
        # 读取Excel文件
        print(f"正在读取文件: {excel_file}")
        
        # 首先查看Excel文件的所有工作表
        excel_data = pd.ExcelFile(excel_file)
        sheet_names = excel_data.sheet_names
        print(f"工作表列表: {sheet_names}")
        
        # 读取第一个工作表
        df = pd.read_excel(excel_file, sheet_name=0)
        print(f"\n数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        print(f"\n前5行数据:")
        print(df.head())
        
        # 检查数据类型
        print(f"\n数据类型:")
        print(df.dtypes)
        
        # 检查是否有数值列
        numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
        print(f"\n数值列: {numeric_columns}")
        
        if len(numeric_columns) == 0:
            print("未找到数值列，尝试转换数据类型...")
            # 尝试转换所有列为数值类型
            for col in df.columns:
                try:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                except:
                    pass
            numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
            print(f"转换后的数值列: {numeric_columns}")
        
        if len(numeric_columns) == 0:
            print("错误: 没有找到可绘制的数值数据")
            return None
            
        # 创建图形
        n_cols = len(numeric_columns)
        if n_cols == 1:
            fig, ax = plt.subplots(1, 1, figsize=(10, 6))
            axes = [ax]
        elif n_cols <= 4:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            axes = axes.flatten()
        else:
            n_rows = (n_cols + 2) // 3
            fig, axes = plt.subplots(n_rows, 3, figsize=(18, 6*n_rows))
            axes = axes.flatten()
        
        fig.suptitle('Sample_1.xlsx 数据分析', fontsize=16, fontweight='bold')
        
        # 为每个数值列绘制图表
        for i, col in enumerate(numeric_columns[:len(axes)]):
            if i < len(axes):
                ax = axes[i]
                
                # 获取非空数据
                data = df[col].dropna()
                
                if len(data) > 0:
                    # 如果数据点较少，绘制散点图和线图
                    if len(data) <= 50:
                        ax.plot(range(len(data)), data, 'o-', linewidth=2, markersize=4, alpha=0.8)
                    else:
                        ax.plot(range(len(data)), data, '-', linewidth=1.5, alpha=0.8)
                    
                    ax.set_title(f'{col}', fontsize=12, fontweight='bold')
                    ax.set_xlabel('数据点索引')
                    ax.set_ylabel(f'{col}')
                    ax.grid(True, alpha=0.3)
                    
                    # 添加统计信息
                    mean_val = data.mean()
                    std_val = data.std()
                    min_val = data.min()
                    max_val = data.max()
                    
                    stats_text = f'均值: {mean_val:.3f}\n标准差: {std_val:.3f}\n范围: {min_val:.3f} - {max_val:.3f}'
                    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                           verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
                           fontsize=9)
                else:
                    ax.text(0.5, 0.5, f'{col}\n(无有效数据)', transform=ax.transAxes, 
                           ha='center', va='center', fontsize=12)
                    ax.set_title(f'{col} (无数据)', fontsize=12)
        
        # 隐藏多余的子图
        for i in range(len(numeric_columns), len(axes)):
            axes[i].set_visible(False)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图片
        output_file = "results/sample1_analysis_plot.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"\n图片已保存至: {output_file}")
        
        # 显示图片
        plt.show()
        
        # 打印详细统计信息
        print("\n=== 详细统计信息 ===")
        print(f"文件: {excel_file}")
        print(f"数据行数: {len(df)}")
        print(f"数据列数: {len(df.columns)}")
        print(f"数值列数量: {len(numeric_columns)}")
        
        for col in numeric_columns:
            data = df[col].dropna()
            if len(data) > 0:
                print(f"\n{col}:")
                print(f"  有效数据点: {len(data)}")
                print(f"  均值: {data.mean():.6f}")
                print(f"  标准差: {data.std():.6f}")
                print(f"  最小值: {data.min():.6f}")
                print(f"  最大值: {data.max():.6f}")
                print(f"  中位数: {data.median():.6f}")
        
        return df
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {excel_file}")
        return None
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # 运行分析函数
    data = analyze_excel_file()
