#!/usr/bin/env python3
"""
温度序列生成器模块

该模块负责：
1. 生成符合约束条件的温度序列
2. 序列平滑处理和插值
3. 物理约束验证
4. 序列质量评估
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy import interpolate
from scipy.ndimage import gaussian_filter1d
from typing import List, Dict, Tuple, Optional
import logging
import yaml

logger = logging.getLogger(__name__)


class SequenceGenerator:
    """温度序列生成器"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化序列生成器
        
        Args:
            config_path: 配置文件路径
        """
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        self.temp_config = self.config['pso']['temperature_sequence']

        # 温度约束参数
        self.min_temp = self.temp_config['min_temperature']
        self.max_temp = self.temp_config['max_temperature']
        self.max_change_rate = self.temp_config['max_change_rate']
        self.sequence_length = self.temp_config['sequence_length']
        self.control_points = self.temp_config['control_points']

        # 变长序列参数
        self.variable_length_config = self.temp_config.get('variable_length', {})
        self.variable_length_enabled = self.variable_length_config.get('enable', False)

        if self.variable_length_enabled:
            self.min_length = self.variable_length_config['min_length']
            self.max_length = self.variable_length_config['max_length']
            self.default_length = self.variable_length_config['default_length']
            self.interpolation_method = self.variable_length_config['interpolation_method']
            self.boundary_handling = self.variable_length_config['boundary_handling']

            # 业务趋势约束参数 (基于21个样本数据集分析)
            self.enforce_business_trend = self.variable_length_config.get('enforce_business_trend', True)
            self.expected_temp_rise_min = self.variable_length_config.get('expected_temp_rise_min', 88)
            self.expected_temp_rise_max = self.variable_length_config.get('expected_temp_rise_max', 130)
            self.start_temp_range = self.variable_length_config.get('start_temp_range', [16.0, 32.0])
            self.end_temp_range = self.variable_length_config.get('end_temp_range', [136.1, 145.4])

            # 阶段性温度控制参数
            self.early_stage_range = self.variable_length_config.get('early_stage_range', [81.3, 138.7])
            self.middle_stage_range = self.variable_length_config.get('middle_stage_range', [113.3, 143.2])
            self.late_stage_range = self.variable_length_config.get('late_stage_range', [126.7, 149.3])

            # 起始阶段稳定性参数
            self.initial_stability_points = self.variable_length_config.get('initial_stability_points', 50)
            self.initial_max_change = self.variable_length_config.get('initial_max_change', 0.1)

            logger.info(f"序列生成器初始化完成 (变长模式)")
            logger.info(f"温度范围: [{self.min_temp}, {self.max_temp}]°C")
            logger.info(f"最大变化率: {self.max_change_rate}°C/step")
            logger.info(f"序列长度范围: [{self.min_length}, {self.max_length}]")
            logger.info(f"插值方法: {self.interpolation_method}")
            logger.info(f"业务趋势约束: {'启用' if self.enforce_business_trend else '禁用'}")
        else:
            # 固定长度模式也使用业务趋势约束 (基于21个样本数据集分析)
            self.enforce_business_trend = True  # 默认启用
            self.expected_temp_rise_min = 88
            self.expected_temp_rise_max = 130
            self.start_temp_range = [16.0, 32.0]
            self.end_temp_range = [136.1, 145.4]

            # 阶段性温度控制参数
            self.early_stage_range = [81.3, 138.7]
            self.middle_stage_range = [113.3, 143.2]
            self.late_stage_range = [126.7, 149.3]

            # 起始阶段稳定性参数
            self.initial_stability_points = 50
            self.initial_max_change = 0.1

            logger.info(f"序列生成器初始化完成 (固定长度模式)")
            logger.info(f"温度范围: [{self.min_temp}, {self.max_temp}]°C")
            logger.info(f"最大变化率: {self.max_change_rate}°C/step")
            logger.info(f"序列长度: {self.sequence_length}")
            logger.info(f"业务趋势约束: 启用 (升温工艺)")
    
    def generate_random_sequence(self, seed: Optional[int] = None) -> np.ndarray:
        """
        生成随机温度序列
        
        Args:
            seed: 随机种子
            
        Returns:
            随机温度序列
        """
        if seed is not None:
            np.random.seed(seed)
        
        # 生成随机控制点
        control_points = np.random.uniform(self.min_temp, self.max_temp, self.control_points)
        
        # 应用变化率约束
        control_points = self._apply_change_rate_constraint(control_points)
        
        # 转换为完整序列
        sequence = self._control_points_to_sequence(control_points)
        
        return sequence
    
    def generate_smooth_sequence(self, start_temp: float, end_temp: float, 
                               pattern: str = "linear") -> np.ndarray:
        """
        生成平滑的温度序列
        
        Args:
            start_temp: 起始温度
            end_temp: 结束温度
            pattern: 变化模式 ("linear", "exponential", "sigmoid", "sinusoidal")
            
        Returns:
            平滑温度序列
        """
        x = np.linspace(0, 1, self.sequence_length)
        
        if pattern == "linear":
            sequence = start_temp + (end_temp - start_temp) * x
            
        elif pattern == "exponential":
            # 指数变化
            alpha = np.log(end_temp / start_temp) if start_temp > 0 else 1
            sequence = start_temp * np.exp(alpha * x)
            
        elif pattern == "sigmoid":
            # S型变化
            sigmoid = 1 / (1 + np.exp(-10 * (x - 0.5)))
            sequence = start_temp + (end_temp - start_temp) * sigmoid
            
        elif pattern == "sinusoidal":
            # 正弦波变化
            base_trend = start_temp + (end_temp - start_temp) * x
            amplitude = (self.max_temp - self.min_temp) * 0.1
            oscillation = amplitude * np.sin(4 * np.pi * x)
            sequence = base_trend + oscillation
            
        else:
            raise ValueError(f"未知的变化模式: {pattern}")
        
        # 应用约束
        sequence = self._apply_constraints(sequence)
        
        return sequence
    
    def generate_multi_stage_sequence(self, stages: List[Dict]) -> np.ndarray:
        """
        生成多阶段温度序列
        
        Args:
            stages: 阶段列表，每个阶段包含 {'temp': 温度, 'duration': 持续时间比例}
            
        Returns:
            多阶段温度序列
        """
        if not stages:
            raise ValueError("阶段列表不能为空")
        
        # 标准化持续时间
        total_duration = sum(stage['duration'] for stage in stages)
        normalized_stages = []
        for stage in stages:
            normalized_stages.append({
                'temp': stage['temp'],
                'duration': stage['duration'] / total_duration
            })
        
        # 生成序列
        sequence = []
        current_pos = 0
        
        for i, stage in enumerate(normalized_stages):
            stage_length = int(stage['duration'] * self.sequence_length)
            
            if i == 0:
                # 第一阶段：从当前位置开始
                stage_temps = np.full(stage_length, stage['temp'])
            else:
                # 后续阶段：从前一阶段平滑过渡
                prev_temp = sequence[-1] if sequence else stage['temp']
                stage_temps = np.linspace(prev_temp, stage['temp'], stage_length)
            
            sequence.extend(stage_temps)
            current_pos += stage_length
        
        # 调整到精确长度
        sequence = np.array(sequence)
        if len(sequence) != self.sequence_length:
            x_old = np.linspace(0, 1, len(sequence))
            x_new = np.linspace(0, 1, self.sequence_length)
            sequence = np.interp(x_new, x_old, sequence)
        
        # 应用约束
        sequence = self._apply_constraints(sequence)
        
        return sequence

    def generate_variable_length_sequence(self, length_param: float,
                                        control_points: np.ndarray) -> np.ndarray:
        """
        生成变长温度序列

        Args:
            length_param: 长度参数 (0-1)，映射到实际长度范围
            control_points: 温度控制点数组

        Returns:
            变长温度序列
        """
        if not self.variable_length_enabled:
            raise ValueError("变长序列模式未启用")

        # 将长度参数映射到实际序列长度
        actual_length = int(self.min_length + length_param * (self.max_length - self.min_length))
        actual_length = max(self.min_length, min(actual_length, self.max_length))

        # 生成变长序列
        sequence = self._control_points_to_variable_sequence(control_points, actual_length)

        # 应用约束
        sequence = self._apply_constraints_variable(sequence)

        return sequence

    def _control_points_to_variable_sequence(self, control_points: np.ndarray,
                                           target_length: int) -> np.ndarray:
        """
        将控制点转换为指定长度的序列

        Args:
            control_points: 控制点数组
            target_length: 目标序列长度

        Returns:
            指定长度的温度序列
        """
        # 控制点对应的位置
        x_control = np.linspace(0, target_length - 1, len(control_points))
        x_sequence = np.arange(target_length)

        # 根据配置选择插值方法
        if self.interpolation_method == "cubic_spline":
            try:
                if self.boundary_handling == "natural":
                    # 自然边界条件的三次样条
                    tck = interpolate.splrep(x_control, control_points, s=0)
                    sequence = interpolate.splev(x_sequence, tck)
                else:
                    # 夹紧边界条件
                    f = interpolate.interp1d(x_control, control_points, kind='cubic',
                                           bounds_error=False, fill_value='extrapolate')
                    sequence = f(x_sequence)
            except Exception as e:
                logger.warning(f"三次样条插值失败，回退到线性插值: {e}")
                sequence = np.interp(x_sequence, x_control, control_points)
        else:
            # 线性插值
            sequence = np.interp(x_sequence, x_control, control_points)

        return sequence

    def _apply_constraints_variable(self, sequence: np.ndarray) -> np.ndarray:
        """
        对变长序列应用约束条件 (包含业务趋势约束)

        Args:
            sequence: 原始序列

        Returns:
            约束后的序列
        """
        constrained_sequence = sequence.copy()

        # 1. 温度范围约束
        constrained_sequence = np.clip(constrained_sequence, self.min_temp, self.max_temp)

        # 2. 业务趋势约束：确保整体上升趋势
        constrained_sequence = self._apply_business_trend_constraint(constrained_sequence)

        # 3. 阶段性温度控制 (基于真实样本分布)
        total_length = len(constrained_sequence)
        early_end = int(total_length * 0.2)
        middle_start = int(total_length * 0.2)
        middle_end = int(total_length * 0.8)
        late_start = int(total_length * 0.8)

        # 初期阶段约束
        for i in range(early_end):
            constrained_sequence[i] = np.clip(constrained_sequence[i],
                                            self.early_stage_range[0],
                                            self.early_stage_range[1])

        # 中期阶段约束
        for i in range(middle_start, middle_end):
            constrained_sequence[i] = np.clip(constrained_sequence[i],
                                            self.middle_stage_range[0],
                                            self.middle_stage_range[1])

        # 后期阶段约束 (增强执行)
        for i in range(late_start, total_length):
            constrained_sequence[i] = np.clip(constrained_sequence[i],
                                            self.late_stage_range[0],
                                            self.late_stage_range[1])

        # 4. 末期温度强化约束 (确保接近样本分布)
        # 基于分析：后期阶段平均最高温度147.8°C，90%分位数150.8°C
        final_portion = int(total_length * 0.05)  # 最后5%的数据点
        if final_portion > 0:
            final_start = total_length - final_portion
            target_final_temp = 147.8  # 基于样本分析的目标温度

            for i in range(final_start, total_length):
                # 渐进式接近目标温度
                progress = (i - final_start) / final_portion
                min_temp = target_final_temp - 5.0 + progress * 5.0  # 从142.8°C渐进到147.8°C
                max_temp = self.late_stage_range[1]  # 150.9°C

                constrained_sequence[i] = np.clip(constrained_sequence[i], min_temp, max_temp)

        # 5. 变化率约束 (考虑序列长度和起始阶段稳定性)
        initial_points = getattr(self, 'initial_stability_points', 50)
        initial_max_change = getattr(self, 'initial_max_change', 0.1)

        for i in range(1, len(constrained_sequence)):
            change = constrained_sequence[i] - constrained_sequence[i-1]

            # 起始阶段使用更严格的变化率约束
            if i <= initial_points:
                max_allowed_change = initial_max_change
            else:
                max_allowed_change = self.max_change_rate

            if abs(change) > max_allowed_change:
                sign = 1 if change > 0 else -1
                constrained_sequence[i] = constrained_sequence[i-1] + sign * max_allowed_change

        return constrained_sequence

    def _apply_business_trend_constraint(self, sequence: np.ndarray) -> np.ndarray:
        """
        应用业务趋势约束：确保温度序列整体呈上升趋势

        基于数据集分析：100%的样本都是上升趋势，平均温度变化+109.39°C

        Args:
            sequence: 原始序列

        Returns:
            符合业务趋势的序列
        """
        if len(sequence) < 2:
            return sequence

        constrained_sequence = sequence.copy()

        # 如果未启用业务趋势约束，直接返回
        if not getattr(self, 'enforce_business_trend', True):
            return constrained_sequence

        # 计算期望的总体上升幅度 (基于配置和数据集统计)
        start_temp = constrained_sequence[0]

        # 确保起始温度在合理范围内 (基于样本数据分析，进一步降低)
        start_temp_min, start_temp_max = getattr(self, 'start_temp_range', [16.0, 32.0])
        if start_temp > start_temp_max or start_temp < start_temp_min:
            start_temp = np.random.uniform(start_temp_min, start_temp_max)
            constrained_sequence[0] = start_temp

        # 计算目标结束温度 (基于样本数据分析: 88-130°C上升幅度)
        temp_rise_min = getattr(self, 'expected_temp_rise_min', 88)
        temp_rise_max = getattr(self, 'expected_temp_rise_max', 130)
        target_temp_rise = np.random.uniform(temp_rise_min, temp_rise_max)

        # 确保结束温度在合理范围内
        end_temp_min, end_temp_max = getattr(self, 'end_temp_range', [136.1, 145.4])
        target_end_temp = np.clip(start_temp + target_temp_rise, end_temp_min, min(end_temp_max, self.max_temp))

        # 使用单调递增插值确保整体上升趋势
        sequence_length = len(constrained_sequence)

        # 生成单调递增的基础趋势
        base_trend = np.linspace(start_temp, target_end_temp, sequence_length)

        # 保留原序列的局部变化特征，但确保整体趋势
        # 计算原序列相对于线性趋势的偏差
        original_trend = np.linspace(constrained_sequence[0], constrained_sequence[-1], sequence_length)
        deviations = constrained_sequence - original_trend

        # 限制偏差幅度，避免破坏整体上升趋势
        max_deviation = (target_end_temp - start_temp) * 0.1  # 允许10%的偏差
        deviations = np.clip(deviations, -max_deviation, max_deviation)

        # 应用约束后的序列
        constrained_sequence = base_trend + deviations

        # 最终确保单调性（允许小幅波动但整体上升）
        for i in range(1, len(constrained_sequence)):
            # 确保每个点不低于前一个点太多
            min_allowed = constrained_sequence[i-1] - 2.0  # 允许小幅下降
            constrained_sequence[i] = max(constrained_sequence[i], min_allowed)

        return constrained_sequence

    def _control_points_to_sequence(self, control_points: np.ndarray) -> np.ndarray:
        """
        将控制点转换为完整序列
        
        Args:
            control_points: 控制点数组
            
        Returns:
            完整温度序列
        """
        # 控制点对应的位置
        x_control = np.linspace(0, self.sequence_length - 1, len(control_points))
        x_sequence = np.arange(self.sequence_length)
        
        # 使用三次样条插值
        try:
            f = interpolate.interp1d(x_control, control_points, kind='cubic', 
                                   bounds_error=False, fill_value='extrapolate')
            sequence = f(x_sequence)
        except:
            # 如果三次插值失败，使用线性插值
            sequence = np.interp(x_sequence, x_control, control_points)
        
        return sequence
    
    def _apply_change_rate_constraint(self, control_points: np.ndarray) -> np.ndarray:
        """
        应用温度变化率约束到控制点
        
        Args:
            control_points: 原始控制点
            
        Returns:
            约束后的控制点
        """
        constrained_points = control_points.copy()
        
        # 计算控制点之间的时间间隔
        time_interval = self.sequence_length / (len(control_points) - 1)
        max_change_per_interval = self.max_change_rate * time_interval
        
        for i in range(1, len(constrained_points)):
            change = constrained_points[i] - constrained_points[i-1]
            
            if abs(change) > max_change_per_interval:
                # 限制变化幅度
                sign = 1 if change > 0 else -1
                constrained_points[i] = constrained_points[i-1] + sign * max_change_per_interval
        
        return constrained_points
    
    def _apply_constraints(self, sequence: np.ndarray) -> np.ndarray:
        """
        应用所有约束条件
        
        Args:
            sequence: 原始序列
            
        Returns:
            约束后的序列
        """
        constrained_sequence = sequence.copy()
        
        # 1. 温度范围约束
        constrained_sequence = np.clip(constrained_sequence, self.min_temp, self.max_temp)

        # 2. 业务趋势约束：确保整体上升趋势
        constrained_sequence = self._apply_business_trend_constraint(constrained_sequence)

        # 3. 阶段性温度控制 (基于真实样本分布)
        total_length = len(constrained_sequence)
        early_end = int(total_length * 0.2)
        middle_start = int(total_length * 0.2)
        middle_end = int(total_length * 0.8)
        late_start = int(total_length * 0.8)

        # 初期阶段约束
        for i in range(early_end):
            constrained_sequence[i] = np.clip(constrained_sequence[i],
                                            self.early_stage_range[0],
                                            self.early_stage_range[1])

        # 中期阶段约束
        for i in range(middle_start, middle_end):
            constrained_sequence[i] = np.clip(constrained_sequence[i],
                                            self.middle_stage_range[0],
                                            self.middle_stage_range[1])

        # 后期阶段约束 (增强执行)
        for i in range(late_start, total_length):
            constrained_sequence[i] = np.clip(constrained_sequence[i],
                                            self.late_stage_range[0],
                                            self.late_stage_range[1])

        # 4. 末期温度强化约束 (确保接近样本分布)
        final_portion = int(total_length * 0.05)  # 最后5%的数据点
        if final_portion > 0:
            final_start = total_length - final_portion
            target_final_temp = 147.8  # 基于样本分析的目标温度

            for i in range(final_start, total_length):
                # 渐进式接近目标温度
                progress = (i - final_start) / final_portion
                min_temp = target_final_temp - 5.0 + progress * 5.0  # 从142.8°C渐进到147.8°C
                max_temp = self.late_stage_range[1]  # 150.9°C

                constrained_sequence[i] = np.clip(constrained_sequence[i], min_temp, max_temp)

        # 5. 变化率约束 (包含起始阶段稳定性)
        initial_points = getattr(self, 'initial_stability_points', 50)
        initial_max_change = getattr(self, 'initial_max_change', 0.1)

        for i in range(1, len(constrained_sequence)):
            change = constrained_sequence[i] - constrained_sequence[i-1]

            # 起始阶段使用更严格的变化率约束
            if i <= initial_points:
                max_allowed_change = initial_max_change
            else:
                max_allowed_change = self.max_change_rate

            if abs(change) > max_allowed_change:
                sign = 1 if change > 0 else -1
                constrained_sequence[i] = constrained_sequence[i-1] + sign * max_allowed_change

        return constrained_sequence
    
    def smooth_sequence(self, sequence: np.ndarray, sigma: float = 1.0) -> np.ndarray:
        """
        平滑温度序列
        
        Args:
            sequence: 原始序列
            sigma: 高斯滤波器标准差
            
        Returns:
            平滑后的序列
        """
        smoothed = gaussian_filter1d(sequence, sigma=sigma)
        
        # 重新应用约束
        smoothed = self._apply_constraints(smoothed)
        
        return smoothed
    
    def validate_sequence(self, sequence: np.ndarray) -> Dict[str, bool]:
        """
        验证序列是否满足所有约束条件 (支持变长序列)

        Args:
            sequence: 温度序列

        Returns:
            验证结果字典
        """
        validation_results = {}

        # 1. 温度范围检查
        temp_range_valid = np.all((sequence >= self.min_temp) & (sequence <= self.max_temp))
        validation_results['temperature_range'] = temp_range_valid

        # 2. 变化率检查
        changes = np.abs(np.diff(sequence))
        change_rate_valid = np.all(changes <= self.max_change_rate)
        validation_results['change_rate'] = change_rate_valid

        # 3. 序列长度检查
        if self.variable_length_enabled:
            length_valid = (self.min_length <= len(sequence) <= self.max_length)
        else:
            length_valid = len(sequence) == self.sequence_length
        validation_results['sequence_length'] = length_valid

        # 4. 数值有效性检查
        numeric_valid = not (np.any(np.isnan(sequence)) or np.any(np.isinf(sequence)))
        validation_results['numeric_validity'] = numeric_valid

        # 5. 业务趋势检查：确保整体上升趋势
        if len(sequence) >= 2:
            overall_trend = sequence[-1] - sequence[0]
            trend_valid = overall_trend > 0  # 要求整体上升
            validation_results['business_trend'] = trend_valid
        else:
            validation_results['business_trend'] = True

        # 总体有效性
        validation_results['overall_valid'] = all(validation_results.values())

        return validation_results
    
    def analyze_sequence_quality(self, sequence: np.ndarray) -> Dict:
        """
        分析序列质量指标
        
        Args:
            sequence: 温度序列
            
        Returns:
            质量分析结果
        """
        analysis = {}
        
        # 基本统计
        analysis['mean_temperature'] = np.mean(sequence)
        analysis['std_temperature'] = np.std(sequence)
        analysis['min_temperature'] = np.min(sequence)
        analysis['max_temperature'] = np.max(sequence)
        analysis['temperature_range'] = analysis['max_temperature'] - analysis['min_temperature']
        
        # 变化特性
        changes = np.diff(sequence)
        analysis['mean_change_rate'] = np.mean(np.abs(changes))
        analysis['max_change_rate'] = np.max(np.abs(changes))
        analysis['change_rate_std'] = np.std(changes)
        
        # 平滑性指标
        second_diff = np.diff(sequence, n=2)
        analysis['smoothness'] = 1.0 / (1.0 + np.var(second_diff))
        
        # 趋势分析
        x = np.arange(len(sequence))
        trend_coef = np.polyfit(x, sequence, 1)[0]
        analysis['overall_trend'] = trend_coef
        
        # 约束满足度
        validation = self.validate_sequence(sequence)
        analysis['constraint_satisfaction'] = validation
        
        return analysis
    
    def plot_sequence(self, sequence: np.ndarray, title: str = "温度序列", 
                     save_path: Optional[str] = None):
        """
        绘制温度序列图
        
        Args:
            sequence: 温度序列
            title: 图表标题
            save_path: 保存路径
        """
        plt.figure(figsize=(12, 6))
        
        # 主序列图
        plt.subplot(2, 1, 1)
        plt.plot(sequence, 'b-', linewidth=2, label='温度序列')
        plt.axhline(y=self.min_temp, color='r', linestyle='--', alpha=0.7, label='最低温度')
        plt.axhline(y=self.max_temp, color='r', linestyle='--', alpha=0.7, label='最高温度')
        plt.xlabel('时间步')
        plt.ylabel('温度 (°C)')
        plt.title(title)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 变化率图
        plt.subplot(2, 1, 2)
        changes = np.diff(sequence)
        plt.plot(changes, 'g-', linewidth=1, label='温度变化率')
        plt.axhline(y=self.max_change_rate, color='r', linestyle='--', alpha=0.7, label='最大变化率')
        plt.axhline(y=-self.max_change_rate, color='r', linestyle='--', alpha=0.7)
        plt.xlabel('时间步')
        plt.ylabel('变化率 (°C/step)')
        plt.title('温度变化率')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"序列图表已保存到 {save_path}")
        
        plt.show()


def main():
    """测试序列生成器功能"""
    generator = SequenceGenerator()
    
    # 测试随机序列生成
    random_seq = generator.generate_random_sequence(seed=42)
    print(f"随机序列长度: {len(random_seq)}")
    
    # 测试平滑序列生成
    smooth_seq = generator.generate_smooth_sequence(20, 150, "sigmoid")
    print(f"平滑序列长度: {len(smooth_seq)}")
    
    # 测试多阶段序列
    stages = [
        {'temp': 25, 'duration': 0.3},
        {'temp': 100, 'duration': 0.4},
        {'temp': 80, 'duration': 0.3}
    ]
    multi_stage_seq = generator.generate_multi_stage_sequence(stages)
    print(f"多阶段序列长度: {len(multi_stage_seq)}")
    
    # 验证序列
    validation = generator.validate_sequence(random_seq)
    print(f"随机序列验证结果: {validation}")
    
    # 分析序列质量
    quality = generator.analyze_sequence_quality(smooth_seq)
    print(f"平滑序列质量分析: {quality}")


if __name__ == "__main__":
    main()
