#!/usr/bin/env python3
"""
混合适应度评估器模块

该模块负责：
1. 集成分类学习和统计学两种适应度评估方法
2. 提供可配置的权重分配机制
3. 实现优雅的降级策略和缓存机制
4. 确保高性能和向后兼容性
"""

import numpy as np
import pandas as pd
import yaml
import logging
import os
import joblib
from typing import Dict, List, Tuple, Optional, Callable, Any
from pathlib import Path
import time
import hashlib

try:
    from .fitness_evaluator import FitnessEvaluator
    from .sequence_classifier import SequenceClassifier
    from .feature_extractor import FeatureExtractor
except ImportError:
    from fitness_evaluator import FitnessEvaluator
    from sequence_classifier import SequenceClassifier
    from feature_extractor import FeatureExtractor

logger = logging.getLogger(__name__)


class HybridFitnessEvaluator:
    """混合适应度评估器"""
    
    def __init__(self, 
                 config_path: str = "config/config.yaml",
                 model_dir: str = "models",
                 statistical_fitness_function: Optional[Callable] = None,
                 reference_sequences: Optional[List[np.ndarray]] = None):
        """
        初始化混合适应度评估器
        
        Args:
            config_path: 配置文件路径
            model_dir: 模型文件目录
            statistical_fitness_function: 统计学适应度函数
            reference_sequences: 参考序列列表（用于分类器评估）
        """
        self.logger = logging.getLogger(__name__)
        
        # 加载配置
        self.config = self._load_config(config_path)
        self.hybrid_config = self.config.get('pso', {}).get('hybrid_fitness', {})
        
        # 基本配置
        self.model_dir = model_dir
        self.enable_hybrid = self.hybrid_config.get('enable', True)
        self.classifier_weight = self.hybrid_config.get('classifier_weight', 0.6)
        self.statistical_weight = self.hybrid_config.get('statistical_weight', 0.4)
        
        # 分类器配置
        self.classifier_config = self.hybrid_config.get('classifier', {})
        self.enable_cache = self.classifier_config.get('enable_cache', True)
        self.cache_size = self.classifier_config.get('cache_size', 1000)
        self.fallback_to_statistical = self.classifier_config.get('fallback_to_statistical', True)
        self.fallback_warning = self.classifier_config.get('fallback_warning', True)
        
        # 性能配置
        self.performance_config = self.hybrid_config.get('performance', {})
        self.max_sequence_length = self.performance_config.get('max_sequence_length', 50000)
        
        # 组件初始化
        self.statistical_fitness_function = statistical_fitness_function
        self.reference_sequences = reference_sequences or []
        
        # 分类器组件
        self.classifier = None
        self.feature_extractor = None
        self.fitness_evaluator = None
        self.classifier_available = False
        
        # 缓存机制
        self.fitness_cache = {} if self.enable_cache else None
        self.cache_hits = 0
        self.cache_misses = 0
        
        # 性能统计
        self.evaluation_times = {
            'classifier': [],
            'statistical': [],
            'total': []
        }
        
        # 初始化分类器
        self._initialize_classifier()
        
        self.logger.info(f"混合适应度评估器初始化完成")
        self.logger.info(f"  混合评估: {'启用' if self.enable_hybrid else '禁用'}")
        self.logger.info(f"  分类器权重: {self.classifier_weight:.1%}")
        self.logger.info(f"  统计学权重: {self.statistical_weight:.1%}")
        self.logger.info(f"  分类器可用: {'是' if self.classifier_available else '否'}")
        self.logger.info(f"  缓存: {'启用' if self.enable_cache else '禁用'}")
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.warning(f"加载配置文件失败: {e}，使用默认配置")
            return {}
    
    def _initialize_classifier(self):
        """初始化分类器组件"""
        if not self.enable_hybrid:
            self.logger.info("混合评估已禁用，跳过分类器初始化")
            return
        
        try:
            # 构建模型文件路径
            classifier_file = self.classifier_config.get('classifier_file', 'sequence_classifier.joblib')
            feature_extractor_file = self.classifier_config.get('feature_extractor_file', 'feature_extractor.joblib')
            metadata_file = self.classifier_config.get('metadata_file', 'sequence_classifier_metadata.joblib')
            
            classifier_path = os.path.join(self.model_dir, classifier_file)
            feature_extractor_path = os.path.join(self.model_dir, feature_extractor_file)
            metadata_path = os.path.join(self.model_dir, metadata_file)
            
            # 检查文件是否存在
            if not all(os.path.exists(path) for path in [classifier_path, feature_extractor_path]):
                raise FileNotFoundError(f"模型文件不存在: {classifier_path} 或 {feature_extractor_path}")
            
            # 加载分类器
            self.classifier = joblib.load(classifier_path)
            self.logger.info(f"分类器已加载: {classifier_path}")
            
            # 加载特征提取器
            self.feature_extractor = joblib.load(feature_extractor_path)
            self.logger.info(f"特征提取器已加载: {feature_extractor_path}")
            
            # 加载元数据（如果存在）
            if os.path.exists(metadata_path):
                metadata = joblib.load(metadata_path)
                self.logger.info(f"元数据已加载: {metadata_path}")
            
            # 创建适应度评估器
            # 即使没有参考序列，也可以创建适应度评估器用于内在质量评估
            self.fitness_evaluator = FitnessEvaluator(
                classifier=self.classifier,
                feature_extractor=self.feature_extractor,
                reference_sequences=self.reference_sequences,  # 可以为空列表
                config_path=self.config.get('config_path', 'config/config.yaml')
            )

            # 设置评估策略
            evaluation_strategy = self.classifier_config.get('evaluation_strategy', 'ensemble')
            self.fitness_evaluator.evaluation_strategy = evaluation_strategy

            # 设置比较数量
            if len(self.reference_sequences) > 0:
                num_comparisons = self.classifier_config.get('num_comparisons', 5)
                self.fitness_evaluator.num_comparisons = min(num_comparisons, len(self.reference_sequences))
                self.logger.info(f"适应度评估器创建成功，参考序列数量: {len(self.reference_sequences)}")
            else:
                self.fitness_evaluator.num_comparisons = 0
                self.logger.info("适应度评估器创建成功，将使用内在质量评估（无参考序列）")

            self.classifier_available = True
                
        except Exception as e:
            self.logger.error(f"分类器初始化失败: {e}")
            if self.fallback_warning:
                self.logger.warning("将使用纯统计学适应度评估方法")
            self.classifier_available = False
    
    def _sequence_to_key(self, sequence: np.ndarray) -> str:
        """将序列转换为缓存键"""
        if not self.enable_cache:
            return ""
        
        # 使用序列的哈希值作为缓存键
        sequence_bytes = sequence.astype(np.float32).tobytes()
        return hashlib.md5(sequence_bytes).hexdigest()
    
    def _check_cache(self, cache_key: str) -> Optional[float]:
        """检查缓存"""
        if not self.enable_cache or not cache_key:
            return None
        
        if cache_key in self.fitness_cache:
            self.cache_hits += 1
            return self.fitness_cache[cache_key]
        
        self.cache_misses += 1
        return None
    
    def _update_cache(self, cache_key: str, fitness: float):
        """更新缓存"""
        if not self.enable_cache or not cache_key:
            return
        
        # 如果缓存已满，删除最旧的条目
        if len(self.fitness_cache) >= self.cache_size:
            oldest_key = next(iter(self.fitness_cache))
            del self.fitness_cache[oldest_key]
        
        self.fitness_cache[cache_key] = fitness
    
    def _preprocess_sequence(self, sequence: np.ndarray) -> np.ndarray:
        """预处理序列"""
        # 限制序列长度
        if len(sequence) > self.max_sequence_length:
            # 使用等间距采样
            indices = np.linspace(0, len(sequence) - 1, self.max_sequence_length, dtype=int)
            sequence = sequence[indices]
            self.logger.debug(f"序列长度超限，已重采样至 {self.max_sequence_length} 点")
        
        return sequence

    def evaluate_classifier_fitness(self, sequence: np.ndarray) -> float:
        """
        使用分类器评估序列适应度

        Args:
            sequence: 温度序列

        Returns:
            分类器适应度分数 (0-1)
        """
        if not self.classifier_available or not self.fitness_evaluator:
            return 0.5  # 返回中性分数

        try:
            start_time = time.time()

            # 预处理序列
            processed_sequence = self._preprocess_sequence(sequence)

            # 使用适应度评估器评估
            fitness = self.fitness_evaluator.evaluate_fitness(processed_sequence)

            # 记录评估时间
            evaluation_time = time.time() - start_time
            self.evaluation_times['classifier'].append(evaluation_time)

            return float(fitness)

        except Exception as e:
            self.logger.warning(f"分类器适应度评估失败: {e}")
            return 0.5

    def evaluate_statistical_fitness(self, sequence: np.ndarray) -> Tuple[float, Dict]:
        """
        使用统计学方法评估序列适应度

        Args:
            sequence: 温度序列

        Returns:
            统计学适应度分数和详细分解
        """
        if not self.statistical_fitness_function:
            # 如果没有提供统计学函数，返回默认分数
            return 0.5, {}

        try:
            start_time = time.time()

            # 调用统计学适应度函数
            if callable(self.statistical_fitness_function):
                result = self.statistical_fitness_function(sequence)

                # 处理不同的返回格式
                if isinstance(result, tuple) and len(result) == 2:
                    fitness, breakdown = result
                elif isinstance(result, (int, float)):
                    fitness, breakdown = float(result), {}
                else:
                    fitness, breakdown = 0.5, {}
            else:
                fitness, breakdown = 0.5, {}

            # 记录评估时间
            evaluation_time = time.time() - start_time
            self.evaluation_times['statistical'].append(evaluation_time)

            return float(fitness), breakdown

        except Exception as e:
            self.logger.warning(f"统计学适应度评估失败: {e}")
            return 0.5, {}

    def evaluate_hybrid_fitness(self, sequence: np.ndarray) -> Tuple[float, Dict]:
        """
        混合适应度评估

        Args:
            sequence: 温度序列

        Returns:
            混合适应度分数和详细分解
        """
        start_time = time.time()

        # 检查缓存
        cache_key = self._sequence_to_key(sequence)
        cached_fitness = self._check_cache(cache_key)
        if cached_fitness is not None:
            return cached_fitness, {'cached': True}

        # 初始化结果
        breakdown = {
            'classifier_fitness': 0.0,
            'statistical_fitness': 0.0,
            'classifier_weight': self.classifier_weight,
            'statistical_weight': self.statistical_weight,
            'classifier_available': self.classifier_available,
            'hybrid_enabled': self.enable_hybrid
        }

        # 如果混合评估被禁用，只使用统计学方法
        if not self.enable_hybrid:
            statistical_fitness, statistical_breakdown = self.evaluate_statistical_fitness(sequence)
            breakdown.update(statistical_breakdown)
            breakdown['statistical_fitness'] = statistical_fitness
            final_fitness = statistical_fitness
        else:
            # 分类器评估
            if self.classifier_available:
                classifier_fitness = self.evaluate_classifier_fitness(sequence)
                breakdown['classifier_fitness'] = classifier_fitness
            else:
                classifier_fitness = 0.0
                breakdown['classifier_fitness'] = 0.0
                if self.fallback_to_statistical:
                    # 降级到纯统计学方法
                    self.logger.debug("分类器不可用，降级到纯统计学方法")
                    statistical_fitness, statistical_breakdown = self.evaluate_statistical_fitness(sequence)
                    breakdown.update(statistical_breakdown)
                    breakdown['statistical_fitness'] = statistical_fitness
                    final_fitness = statistical_fitness

                    # 记录总评估时间
                    total_time = time.time() - start_time
                    self.evaluation_times['total'].append(total_time)

                    # 更新缓存
                    self._update_cache(cache_key, final_fitness)

                    return final_fitness, breakdown

            # 统计学评估
            statistical_fitness, statistical_breakdown = self.evaluate_statistical_fitness(sequence)
            breakdown.update(statistical_breakdown)
            breakdown['statistical_fitness'] = statistical_fitness

            # 计算加权平均
            if self.classifier_available:
                final_fitness = (classifier_fitness * self.classifier_weight +
                               statistical_fitness * self.statistical_weight)
            else:
                final_fitness = statistical_fitness

        breakdown['final_fitness'] = final_fitness

        # 记录总评估时间
        total_time = time.time() - start_time
        self.evaluation_times['total'].append(total_time)

        # 更新缓存
        self._update_cache(cache_key, final_fitness)

        return final_fitness, breakdown

    def set_reference_sequences(self, reference_sequences: List[np.ndarray]):
        """
        设置参考序列

        Args:
            reference_sequences: 参考序列列表
        """
        self.reference_sequences = reference_sequences

        # 重新初始化适应度评估器
        if self.classifier_available and len(reference_sequences) > 0:
            try:
                self.fitness_evaluator = FitnessEvaluator(
                    classifier=self.classifier,
                    feature_extractor=self.feature_extractor,
                    reference_sequences=reference_sequences,
                    config_path=self.config.get('config_path', 'config/config.yaml')
                )

                # 重新设置配置
                evaluation_strategy = self.classifier_config.get('evaluation_strategy', 'ensemble')
                self.fitness_evaluator.evaluation_strategy = evaluation_strategy

                num_comparisons = self.classifier_config.get('num_comparisons', 5)
                self.fitness_evaluator.num_comparisons = min(num_comparisons, len(reference_sequences))

                self.logger.info(f"参考序列已更新，数量: {len(reference_sequences)}")

            except Exception as e:
                self.logger.error(f"更新参考序列失败: {e}")
                self.classifier_available = False

    def set_weights(self, classifier_weight: float, statistical_weight: float):
        """
        设置权重

        Args:
            classifier_weight: 分类器权重
            statistical_weight: 统计学权重
        """
        # 权重归一化
        total_weight = classifier_weight + statistical_weight
        if total_weight > 0:
            self.classifier_weight = classifier_weight / total_weight
            self.statistical_weight = statistical_weight / total_weight
        else:
            self.classifier_weight = 0.6
            self.statistical_weight = 0.4

        self.logger.info(f"权重已更新: 分类器={self.classifier_weight:.1%}, 统计学={self.statistical_weight:.1%}")

    def get_performance_stats(self) -> Dict:
        """
        获取性能统计信息

        Returns:
            性能统计字典
        """
        stats = {
            'cache_stats': {
                'hits': self.cache_hits,
                'misses': self.cache_misses,
                'hit_rate': self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0,
                'cache_size': len(self.fitness_cache) if self.fitness_cache else 0
            },
            'evaluation_times': {},
            'classifier_available': self.classifier_available,
            'hybrid_enabled': self.enable_hybrid
        }

        # 计算平均评估时间
        for eval_type, times in self.evaluation_times.items():
            if times:
                stats['evaluation_times'][eval_type] = {
                    'count': len(times),
                    'mean': np.mean(times),
                    'std': np.std(times),
                    'min': np.min(times),
                    'max': np.max(times)
                }
            else:
                stats['evaluation_times'][eval_type] = {
                    'count': 0,
                    'mean': 0,
                    'std': 0,
                    'min': 0,
                    'max': 0
                }

        return stats

    def clear_cache(self):
        """清空缓存"""
        if self.fitness_cache:
            self.fitness_cache.clear()
            self.cache_hits = 0
            self.cache_misses = 0
            self.logger.info("适应度缓存已清空")

    def __call__(self, sequence: np.ndarray) -> float:
        """
        使混合适应度评估器可调用

        Args:
            sequence: 温度序列

        Returns:
            适应度分数
        """
        fitness, _ = self.evaluate_hybrid_fitness(sequence)
        return fitness


def main():
    """测试混合适应度评估器"""
    # 创建测试数据
    test_sequence = np.random.uniform(20, 150, 1000)
    reference_sequences = [
        np.random.uniform(20, 150, 1000) for _ in range(5)
    ]

    # 创建混合评估器
    evaluator = HybridFitnessEvaluator(
        reference_sequences=reference_sequences
    )

    # 测试评估
    fitness, breakdown = evaluator.evaluate_hybrid_fitness(test_sequence)
    print(f"混合适应度分数: {fitness:.4f}")
    print(f"详细分解: {breakdown}")

    # 性能统计
    stats = evaluator.get_performance_stats()
    print(f"性能统计: {stats}")


if __name__ == "__main__":
    main()
