#!/usr/bin/env python3
"""
适应度评估器模块

该模块负责：
1. 基于训练好的分类器评估温度序列质量
2. 提供多种适应度计算策略
3. 处理序列质量的相对比较
"""

import numpy as np
from typing import List, Dict, Callable, Optional
import logging
import yaml
import random

logger = logging.getLogger(__name__)


class FitnessEvaluator:
    """适应度评估器"""
    
    def __init__(self, classifier, feature_extractor, reference_sequences: List[np.ndarray],
                 config_path: str = "config/config.yaml"):
        """
        初始化适应度评估器
        
        Args:
            classifier: 训练好的序列分类器
            feature_extractor: 特征提取器
            reference_sequences: 参考序列列表（用于比较）
            config_path: 配置文件路径
        """
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)

        self.classifier = classifier
        self.feature_extractor = feature_extractor
        self.reference_sequences = reference_sequences

        # 评估策略配置
        self.evaluation_strategy = "ensemble"  # single, multiple, ensemble
        self.num_comparisons = min(5, len(reference_sequences))  # 每次比较的参考序列数量

        # 变长序列配置
        self.temp_config = self.config['pso']['temperature_sequence']
        self.variable_length_config = self.temp_config.get('variable_length', {})
        self.variable_length_enabled = self.variable_length_config.get('enable', False)

        if self.variable_length_enabled:
            self.min_length = self.variable_length_config['min_length']
            self.max_length = self.variable_length_config['max_length']
            self.default_length = self.variable_length_config['default_length']
            self.length_weight = self.variable_length_config['length_weight']
            self.length_penalty_factor = self.variable_length_config['length_penalty_factor']

            logger.info(f"适应度评估器启用变长序列支持")
            logger.info(f"长度范围: [{self.min_length}, {self.max_length}]")
            logger.info(f"长度权重: {self.length_weight}")

        # 缓存机制
        self.fitness_cache = {}
        self.cache_enabled = True

        logger.info(f"适应度评估器初始化完成，参考序列数量: {len(reference_sequences)}")
    
    def _sequence_to_key(self, sequence: np.ndarray) -> str:
        """
        将序列转换为缓存键
        
        Args:
            sequence: 温度序列
            
        Returns:
            缓存键字符串
        """
        # 使用序列的哈希值作为键
        return str(hash(sequence.tobytes()))
    
    def _calculate_length_penalty(self, sequence: np.ndarray) -> float:
        """
        计算序列长度惩罚

        Args:
            sequence: 温度序列

        Returns:
            长度惩罚分数 (0-1, 1表示无惩罚)
        """
        if not self.variable_length_enabled:
            return 1.0

        seq_length = len(sequence)

        # 计算长度偏离程度
        if seq_length < self.min_length or seq_length > self.max_length:
            # 超出范围的严重惩罚
            return 0.1

        # 计算相对于默认长度的偏离
        length_deviation = abs(seq_length - self.default_length) / self.default_length

        # 应用惩罚因子
        penalty = max(0.0, 1.0 - length_deviation * self.length_penalty_factor)

        return penalty

    def _normalize_sequences_for_comparison(self, seq1: np.ndarray,
                                          seq2: np.ndarray) -> tuple:
        """
        为比较标准化不同长度的序列

        Args:
            seq1: 第一个序列
            seq2: 第二个序列

        Returns:
            标准化后的序列对
        """
        if not self.variable_length_enabled or len(seq1) == len(seq2):
            return seq1, seq2

        # 选择较短的长度作为目标长度
        target_length = min(len(seq1), len(seq2))

        # 重采样到相同长度
        if len(seq1) != target_length:
            indices = np.linspace(0, len(seq1) - 1, target_length).astype(int)
            seq1_normalized = seq1[indices]
        else:
            seq1_normalized = seq1

        if len(seq2) != target_length:
            indices = np.linspace(0, len(seq2) - 1, target_length).astype(int)
            seq2_normalized = seq2[indices]
        else:
            seq2_normalized = seq2

        return seq1_normalized, seq2_normalized

    def evaluate_single_comparison(self, target_sequence: np.ndarray,
                                 reference_sequence: np.ndarray) -> float:
        """
        评估目标序列与单个参考序列的比较结果 (支持变长序列)

        Args:
            target_sequence: 目标序列
            reference_sequence: 参考序列

        Returns:
            比较得分 (0-1)
        """
        try:
            # 标准化序列长度以便比较
            target_norm, reference_norm = self._normalize_sequences_for_comparison(
                target_sequence, reference_sequence
            )

            # 检查分类器类型并调用相应方法
            if hasattr(self.classifier, 'predict_comparison'):
                # 如果是SequenceClassifier对象
                result = self.classifier.predict_comparison(
                    target_norm, reference_norm, self.feature_extractor
                )
                base_score = result['probability_seq1_better']
            else:
                # 如果是原始的SVC对象，手动构造特征并预测
                pair_data = [{
                    'sequence_1': target_norm,
                    'sequence_2': reference_norm,
                    'label': 0  # 占位符
                }]

                # 提取特征
                features = self.feature_extractor.transform(pair_data)

                # 预测概率
                if hasattr(self.classifier, 'predict_proba'):
                    probability = self.classifier.predict_proba(features)[0]
                    # 返回序列1更好的概率（类别1的概率）
                    base_score = float(probability[1]) if len(probability) > 1 else 0.5
                else:
                    # 如果没有概率预测，使用决策函数
                    decision = self.classifier.decision_function(features)[0]
                    # 将决策函数值转换为概率（sigmoid函数）
                    base_score = 1.0 / (1.0 + np.exp(-decision))

            # 应用长度惩罚
            if self.variable_length_enabled:
                length_penalty = self._calculate_length_penalty(target_sequence)
                final_score = base_score * (1 - self.length_weight) + length_penalty * self.length_weight
            else:
                final_score = base_score

            return float(final_score)

        except Exception as e:
            logger.warning(f"单次比较评估失败: {e}")
            return 0.5  # 返回中性分数
    
    def evaluate_multiple_comparisons(self, target_sequence: np.ndarray) -> float:
        """
        评估目标序列与多个参考序列的比较结果

        Args:
            target_sequence: 目标序列

        Returns:
            综合适应度分数
        """
        if len(self.reference_sequences) == 0:
            # 没有参考序列时，使用内在质量评估
            return self._evaluate_intrinsic_quality(target_sequence)

        # 随机选择参考序列进行比较
        selected_references = random.sample(
            self.reference_sequences,
            min(self.num_comparisons, len(self.reference_sequences))
        )

        comparison_scores = []

        for ref_seq in selected_references:
            score = self.evaluate_single_comparison(target_sequence, ref_seq)
            comparison_scores.append(score)

        # 计算平均得分
        average_score = np.mean(comparison_scores)

        return average_score
    
    def evaluate_ensemble(self, target_sequence: np.ndarray) -> float:
        """
        集成评估策略：结合多种评估方法 (支持变长序列)

        Args:
            target_sequence: 目标序列

        Returns:
            集成适应度分数
        """
        scores = []
        weights = []

        # 根据是否有参考序列调整权重策略
        if len(self.reference_sequences) > 0:
            # 有参考序列时的权重分配
            # 1. 多重比较得分
            multi_score = self.evaluate_multiple_comparisons(target_sequence)
            scores.append(multi_score)
            weights.append(0.6)  # 比较得分权重最高

            # 2. 序列质量内在指标
            intrinsic_score = self._evaluate_intrinsic_quality(target_sequence)
            scores.append(intrinsic_score)
            weights.append(0.2)

            # 3. 稳定性评估
            stability_score = self._evaluate_stability(target_sequence)
            scores.append(stability_score)
            weights.append(0.2)
        else:
            # 没有参考序列时的权重分配
            # 1. 序列质量内在指标（主要评估）
            intrinsic_score = self._evaluate_intrinsic_quality(target_sequence)
            scores.append(intrinsic_score)
            weights.append(0.5)

            # 2. 稳定性评估
            stability_score = self._evaluate_stability(target_sequence)
            scores.append(stability_score)
            weights.append(0.3)

            # 3. 分类器内在评估（如果可用）
            classifier_intrinsic_score = self._evaluate_classifier_intrinsic_quality(target_sequence)
            scores.append(classifier_intrinsic_score)
            weights.append(0.2)

        # 4. 变长序列特有：长度适应性评估
        if self.variable_length_enabled:
            length_score = self._evaluate_length_fitness(target_sequence)
            scores.append(length_score)
            weights.append(self.length_weight)

            # 重新标准化权重
            total_weight = sum(weights)
            weights = [w / total_weight for w in weights]

        # 加权平均
        ensemble_score = np.average(scores, weights=weights)

        return ensemble_score

    def _evaluate_length_fitness(self, sequence: np.ndarray) -> float:
        """
        评估序列长度的适应性

        Args:
            sequence: 温度序列

        Returns:
            长度适应性分数 (0-1)
        """
        if not self.variable_length_enabled:
            return 1.0

        seq_length = len(sequence)

        # 1. 基本长度合规性
        if seq_length < self.min_length or seq_length > self.max_length:
            return 0.0

        # 2. 相对于默认长度的适应性
        length_ratio = seq_length / self.default_length

        # 3. 长度效率评估 (较短序列在质量相同时更优)
        efficiency_bonus = 1.0 / (1.0 + (seq_length - self.min_length) / (self.max_length - self.min_length))

        # 4. 综合长度分数
        length_fitness = 0.7 * (1.0 - abs(length_ratio - 1.0)) + 0.3 * efficiency_bonus

        return max(0.0, min(1.0, length_fitness))

    def _evaluate_classifier_intrinsic_quality(self, sequence: np.ndarray) -> float:
        """
        使用分类器评估序列的内在质量（不依赖参考序列）

        Args:
            sequence: 温度序列

        Returns:
            内在质量分数 (0-1)
        """
        try:
            # 如果分类器有内在质量评估方法，使用它
            if hasattr(self.classifier, 'evaluate_intrinsic_quality'):
                return self.classifier.evaluate_intrinsic_quality(sequence, self.feature_extractor)

            # 否则，使用特征提取器提取特征，然后基于特征评估质量
            if self.feature_extractor:
                # 创建虚拟的比较数据来提取特征
                dummy_data = [{
                    'sequence_1': sequence,
                    'sequence_2': sequence,  # 自己与自己比较
                    'label': 1  # 假设自己比自己好（用于特征提取）
                }]

                features = self.feature_extractor.extract_features(dummy_data)
                if len(features) > 0:
                    # 基于特征的简单质量评估
                    feature_vector = features[0]

                    # 使用特征向量的统计特性来评估质量
                    # 这是一个简化的方法，实际应用中可能需要更复杂的逻辑
                    feature_mean = np.mean(feature_vector)
                    feature_std = np.std(feature_vector)

                    # 归一化到0-1范围
                    quality_score = 1.0 / (1.0 + np.exp(-feature_mean))  # sigmoid函数

                    # 考虑特征的稳定性
                    stability_factor = 1.0 / (1.0 + feature_std)

                    # 综合评分
                    intrinsic_score = 0.7 * quality_score + 0.3 * stability_factor

                    return max(0.0, min(1.0, intrinsic_score))

            # 如果都不可用，返回中性分数
            return 0.5

        except Exception as e:
            logger.warning(f"分类器内在质量评估失败: {e}")
            return 0.5
    
    def _evaluate_intrinsic_quality(self, sequence: np.ndarray) -> float:
        """
        评估序列的内在质量指标
        
        Args:
            sequence: 温度序列
            
        Returns:
            内在质量分数 (0-1)
        """
        try:
            # 1. 温度范围合理性
            temp_range_score = self._evaluate_temperature_range(sequence)
            
            # 2. 变化平滑性
            smoothness_score = self._evaluate_smoothness(sequence)
            
            # 3. 趋势合理性
            trend_score = self._evaluate_trend(sequence)
            
            # 综合评分
            intrinsic_score = np.mean([temp_range_score, smoothness_score, trend_score])
            
            return intrinsic_score
            
        except Exception as e:
            logger.warning(f"内在质量评估失败: {e}")
            return 0.5
    
    def _evaluate_temperature_range(self, sequence: np.ndarray) -> float:
        """评估温度范围的合理性"""
        min_temp = self.config['pso']['temperature_sequence']['min_temperature']
        max_temp = self.config['pso']['temperature_sequence']['max_temperature']
        
        # 检查是否在合理范围内
        if np.all((sequence >= min_temp) & (sequence <= max_temp)):
            # 进一步评估温度分布
            temp_std = np.std(sequence)
            optimal_std = (max_temp - min_temp) * 0.1  # 期望的标准差
            
            # 标准差越接近期望值，得分越高
            std_score = 1.0 - min(abs(temp_std - optimal_std) / optimal_std, 1.0)
            return std_score
        else:
            return 0.0
    
    def _evaluate_smoothness(self, sequence: np.ndarray) -> float:
        """评估序列的平滑性"""
        # 计算一阶和二阶差分
        diff1 = np.diff(sequence)
        diff2 = np.diff(sequence, n=2)
        
        # 平滑性指标：差分的方差越小越平滑
        smoothness1 = 1.0 / (1.0 + np.var(diff1))
        smoothness2 = 1.0 / (1.0 + np.var(diff2))
        
        return (smoothness1 + smoothness2) / 2
    
    def _evaluate_trend(self, sequence: np.ndarray) -> float:
        """评估序列趋势的合理性"""
        # 简单的趋势评估：避免过于剧烈的变化
        max_change_rate = self.config['pso']['temperature_sequence']['max_change_rate']
        
        # 计算最大变化率
        changes = np.abs(np.diff(sequence))
        max_change = np.max(changes)
        
        # 变化率在合理范围内得分更高
        if max_change <= max_change_rate:
            return 1.0
        else:
            return max(0.0, 1.0 - (max_change - max_change_rate) / max_change_rate)
    
    def _evaluate_stability(self, sequence: np.ndarray) -> float:
        """评估序列的稳定性"""
        # 计算序列的变异系数
        cv = np.std(sequence) / np.mean(sequence) if np.mean(sequence) > 0 else 1.0
        
        # 变异系数适中的序列更稳定
        optimal_cv = 0.1  # 期望的变异系数
        stability_score = 1.0 - min(abs(cv - optimal_cv) / optimal_cv, 1.0)
        
        return stability_score
    
    def evaluate_fitness(self, sequence: np.ndarray) -> float:
        """
        评估序列的适应度
        
        Args:
            sequence: 温度序列
            
        Returns:
            适应度分数
        """
        # 检查缓存
        if self.cache_enabled:
            cache_key = self._sequence_to_key(sequence)
            if cache_key in self.fitness_cache:
                return self.fitness_cache[cache_key]
        
        # 根据策略选择评估方法
        if self.evaluation_strategy == "single":
            if len(self.reference_sequences) > 0:
                ref_seq = random.choice(self.reference_sequences)
                fitness = self.evaluate_single_comparison(sequence, ref_seq)
            else:
                fitness = self._evaluate_intrinsic_quality(sequence)
                
        elif self.evaluation_strategy == "multiple":
            fitness = self.evaluate_multiple_comparisons(sequence)
            
        elif self.evaluation_strategy == "ensemble":
            fitness = self.evaluate_ensemble(sequence)
            
        else:
            raise ValueError(f"未知的评估策略: {self.evaluation_strategy}")
        
        # 缓存结果
        if self.cache_enabled:
            self.fitness_cache[cache_key] = fitness
        
        return fitness
    
    def get_fitness_function(self) -> Callable[[np.ndarray], float]:
        """
        获取适应度函数（用于PSO优化器）
        
        Returns:
            适应度评估函数
        """
        return self.evaluate_fitness
    
    def clear_cache(self):
        """清空适应度缓存"""
        self.fitness_cache.clear()
        logger.info("适应度缓存已清空")
    
    def get_cache_stats(self) -> Dict:
        """
        获取缓存统计信息
        
        Returns:
            缓存统计字典
        """
        return {
            'cache_size': len(self.fitness_cache),
            'cache_enabled': self.cache_enabled
        }
    
    def set_evaluation_strategy(self, strategy: str):
        """
        设置评估策略
        
        Args:
            strategy: 评估策略 ("single", "multiple", "ensemble")
        """
        valid_strategies = ["single", "multiple", "ensemble"]
        if strategy not in valid_strategies:
            raise ValueError(f"无效的评估策略: {strategy}. 有效选项: {valid_strategies}")
        
        self.evaluation_strategy = strategy
        logger.info(f"评估策略已设置为: {strategy}")
    
    def benchmark_evaluation_speed(self, test_sequence: np.ndarray, num_tests: int = 100) -> Dict:
        """
        基准测试评估速度
        
        Args:
            test_sequence: 测试序列
            num_tests: 测试次数
            
        Returns:
            性能统计字典
        """
        import time
        
        times = []
        
        for _ in range(num_tests):
            start_time = time.time()
            self.evaluate_fitness(test_sequence)
            end_time = time.time()
            times.append(end_time - start_time)
        
        stats = {
            'mean_time': np.mean(times),
            'std_time': np.std(times),
            'min_time': np.min(times),
            'max_time': np.max(times),
            'total_time': np.sum(times)
        }
        
        logger.info(f"评估速度基准测试完成: 平均时间 {stats['mean_time']:.4f}s")
        
        return stats


def main():
    """测试适应度评估器"""
    # 创建测试数据
    test_sequence = np.random.uniform(20, 150, 1000)
    reference_sequences = [
        np.random.uniform(20, 150, 1000) for _ in range(5)
    ]
    
    # 注意：这里需要实际的分类器和特征提取器
    # evaluator = FitnessEvaluator(classifier, feature_extractor, reference_sequences)
    # fitness = evaluator.evaluate_fitness(test_sequence)
    # print(f"适应度分数: {fitness:.4f}")
    
    print("适应度评估器测试需要训练好的分类器和特征提取器")


if __name__ == "__main__":
    main()
