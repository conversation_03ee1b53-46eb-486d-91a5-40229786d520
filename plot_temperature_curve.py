#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
温度序列曲线图绘制脚本
绘制advanced_temperature_sequence_20250724_212347.csv文件中的温度变化曲线
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def plot_temperature_curve():
    """绘制温度序列曲线图"""
    
    # 读取CSV文件
    csv_file = "results/advanced_temperature_sequence_20250724_212347.csv"
    
    try:
        # 读取数据
        df = pd.read_csv(csv_file)
        print(f"成功读取数据，共{len(df)}个数据点")
        print(f"数据列名: {list(df.columns)}")
        
        # 提取数据
        time_points = df['时间点']
        temperatures = df['温度(°C)']
        time_minutes = df['时间(分钟)']
        fitness = df['适应度']
        iterations = df['迭代次数']
        
        # 创建图形和子图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('化学优化模型 - 温度序列分析', fontsize=16, fontweight='bold')
        
        # 子图1: 温度随时间变化
        ax1.plot(time_minutes, temperatures, 'b-', linewidth=2, alpha=0.8)
        ax1.set_xlabel('时间 (分钟)')
        ax1.set_ylabel('温度 (°C)')
        ax1.set_title('温度随时间变化曲线')
        ax1.grid(True, alpha=0.3)
        ax1.set_xlim(0, max(time_minutes))
        
        # 添加温度范围标注
        temp_min = temperatures.min()
        temp_max = temperatures.max()
        temp_range = temp_max - temp_min
        ax1.axhline(y=temp_min, color='g', linestyle='--', alpha=0.7, label=f'最低温度: {temp_min:.2f}°C')
        ax1.axhline(y=temp_max, color='r', linestyle='--', alpha=0.7, label=f'最高温度: {temp_max:.2f}°C')
        ax1.legend()
        
        # 子图2: 温度分布直方图
        ax2.hist(temperatures, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.set_xlabel('温度 (°C)')
        ax2.set_ylabel('频次')
        ax2.set_title('温度分布直方图')
        ax2.grid(True, alpha=0.3)
        
        # 添加统计信息
        mean_temp = temperatures.mean()
        std_temp = temperatures.std()
        ax2.axvline(x=mean_temp, color='red', linestyle='-', linewidth=2, label=f'平均值: {mean_temp:.2f}°C')
        ax2.axvline(x=mean_temp + std_temp, color='orange', linestyle='--', label=f'+1σ: {mean_temp + std_temp:.2f}°C')
        ax2.axvline(x=mean_temp - std_temp, color='orange', linestyle='--', label=f'-1σ: {mean_temp - std_temp:.2f}°C')
        ax2.legend()
        
        # 子图3: 适应度随时间变化
        ax3.plot(time_minutes, fitness, 'g-', linewidth=2, alpha=0.8)
        ax3.set_xlabel('时间 (分钟)')
        ax3.set_ylabel('适应度')
        ax3.set_title('适应度随时间变化')
        ax3.grid(True, alpha=0.3)
        ax3.set_xlim(0, max(time_minutes))
        
        # 子图4: 温度变化率
        temp_diff = np.diff(temperatures)
        time_diff = time_minutes[1:].values
        ax4.plot(time_diff, temp_diff, 'purple', linewidth=1.5, alpha=0.8)
        ax4.set_xlabel('时间 (分钟)')
        ax4.set_ylabel('温度变化率 (°C/步)')
        ax4.set_title('温度变化率')
        ax4.grid(True, alpha=0.3)
        ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图片
        output_file = "results/temperature_analysis_plot.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"图片已保存至: {output_file}")
        
        # 显示图片
        plt.show()
        
        # 打印统计信息
        print("\n=== 温度序列统计信息 ===")
        print(f"数据点总数: {len(df)}")
        print(f"时间范围: {time_minutes.min():.1f} - {time_minutes.max():.1f} 分钟")
        print(f"温度范围: {temp_min:.3f} - {temp_max:.3f} °C")
        print(f"温度变化幅度: {temp_range:.3f} °C")
        print(f"平均温度: {mean_temp:.3f} °C")
        print(f"温度标准差: {std_temp:.3f} °C")
        print(f"适应度: {fitness.iloc[0]:.6f} (恒定值)")
        print(f"迭代次数: {iterations.iloc[0]} (恒定值)")
        print(f"收敛状态: {df['是否收敛'].iloc[0]}")
        
        return df
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {csv_file}")
        return None
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        return None

if __name__ == "__main__":
    # 运行绘图函数
    data = plot_temperature_curve()
