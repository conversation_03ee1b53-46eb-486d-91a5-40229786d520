#!/usr/bin/env python3
"""
绘制data/Esterification目录中所有21个样本的温度曲线图
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os
from datetime import datetime
import seaborn as sns

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')

def load_sample_data(sample_id):
    """加载单个样本数据"""
    file_path = f"data/Esterification/Sample_{sample_id}.xlsx"
    try:
        df = pd.read_excel(file_path, header=None)
        temp_sequence = df.iloc[:, 0].values
        temp_sequence = temp_sequence[~np.isnan(temp_sequence)]
        return temp_sequence
    except Exception as e:
        print(f"加载Sample_{sample_id}.xlsx失败: {e}")
        return None

def plot_all_samples():
    """绘制所有样本的温度曲线图"""
    
    print("开始加载所有样本数据...")
    
    # 加载所有样本数据
    samples_data = {}
    sample_stats = {}
    
    for sample_id in range(1, 22):
        temp_data = load_sample_data(sample_id)
        if temp_data is not None:
            samples_data[sample_id] = temp_data
            sample_stats[sample_id] = {
                'length': len(temp_data),
                'start_temp': temp_data[0],
                'end_temp': temp_data[-1],
                'min_temp': np.min(temp_data),
                'max_temp': np.max(temp_data),
                'mean_temp': np.mean(temp_data),
                'temp_rise': temp_data[-1] - temp_data[0],
                'std_temp': np.std(temp_data)
            }
            print(f"✓ Sample_{sample_id}: {len(temp_data):,}个数据点, {temp_data[0]:.1f}°C → {temp_data[-1]:.1f}°C")
        else:
            print(f"✗ Sample_{sample_id}: 加载失败")
    
    print(f"\n成功加载 {len(samples_data)} 个样本")
    
    # 创建多个图表
    create_overview_plot(samples_data, sample_stats)
    create_detailed_analysis_plots(samples_data, sample_stats)
    create_statistics_plots(sample_stats)
    
    print("所有图表生成完成！")

def create_overview_plot(samples_data, sample_stats):
    """创建总览图表"""
    
    # 创建大图表 - 所有样本叠加显示
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle('酯化反应温度序列数据 - 21个样本总览', fontsize=18, fontweight='bold')
    
    # 1. 所有样本叠加图（左上）
    ax1 = axes[0, 0]
    
    # 生成颜色映射
    colors = plt.cm.tab20(np.linspace(0, 1, len(samples_data)))
    
    for i, (sample_id, temp_data) in enumerate(samples_data.items()):
        # 为了显示清晰，对长序列进行重采样
        if len(temp_data) > 5000:
            indices = np.linspace(0, len(temp_data)-1, 5000).astype(int)
            temp_resampled = temp_data[indices]
            time_points = np.linspace(0, len(temp_data)-1, 5000)
        else:
            temp_resampled = temp_data
            time_points = np.arange(len(temp_data))
        
        # 标记排除的样本
        if sample_id in [8, 13, 19, 20]:
            ax1.plot(time_points, temp_resampled, 
                    label=f'Sample_{sample_id} (排除)', 
                    color=colors[i], alpha=0.4, linestyle='--', linewidth=1)
        else:
            ax1.plot(time_points, temp_resampled, 
                    label=f'Sample_{sample_id}', 
                    color=colors[i], alpha=0.7, linewidth=1.5)
    
    ax1.set_title('所有样本温度序列叠加图')
    ax1.set_xlabel('时间点')
    ax1.set_ylabel('温度 (°C)')
    ax1.grid(True, alpha=0.3)
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    
    # 2. 平均温度曲线（右上）
    ax2 = axes[0, 1]
    
    # 计算所有样本的平均曲线
    max_length = max(len(data) for data in samples_data.values())
    common_length = min(10000, max_length)  # 限制长度以提高性能
    
    resampled_data = []
    for temp_data in samples_data.values():
        if len(temp_data) > 1:
            indices = np.linspace(0, len(temp_data)-1, common_length).astype(int)
            resampled_data.append(temp_data[indices])
    
    if resampled_data:
        mean_curve = np.mean(resampled_data, axis=0)
        std_curve = np.std(resampled_data, axis=0)
        time_common = np.arange(common_length)
        
        ax2.plot(time_common, mean_curve, 'b-', linewidth=3, label='平均温度曲线')
        ax2.fill_between(time_common, 
                        mean_curve - std_curve, 
                        mean_curve + std_curve, 
                        alpha=0.3, color='blue', label='±1标准差范围')
        
        # 添加一些代表性样本
        representative_samples = [1, 5, 10, 15, 21]
        rep_colors = ['red', 'green', 'orange', 'purple', 'brown']
        
        for i, sample_id in enumerate(representative_samples):
            if sample_id in samples_data:
                temp_data = samples_data[sample_id]
                indices = np.linspace(0, len(temp_data)-1, common_length).astype(int)
                temp_resampled = temp_data[indices]
                ax2.plot(time_common, temp_resampled, 
                        color=rep_colors[i], alpha=0.7, linewidth=2,
                        label=f'Sample_{sample_id}')
    
    ax2.set_title('平均温度曲线与代表性样本')
    ax2.set_xlabel('时间点')
    ax2.set_ylabel('温度 (°C)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 起始vs结束温度散点图（左下）
    ax3 = axes[1, 0]
    
    start_temps = [stats['start_temp'] for stats in sample_stats.values()]
    end_temps = [stats['end_temp'] for stats in sample_stats.values()]
    sample_ids = list(sample_stats.keys())
    
    # 区分排除的样本
    excluded_samples = [8, 13, 19, 20]
    
    for i, sample_id in enumerate(sample_ids):
        if sample_id in excluded_samples:
            ax3.scatter(start_temps[i], end_temps[i], 
                       s=100, alpha=0.6, marker='x', color='red',
                       label='排除样本' if sample_id == excluded_samples[0] else "")
        else:
            ax3.scatter(start_temps[i], end_temps[i], 
                       s=100, alpha=0.8, marker='o', color='blue',
                       label='使用样本' if sample_id == 1 else "")
        
        # 添加样本编号标注
        ax3.annotate(f'S{sample_id}', 
                    (start_temps[i], end_temps[i]), 
                    xytext=(5, 5), textcoords='offset points',
                    fontsize=8, alpha=0.8)
    
    ax3.set_title('起始温度 vs 结束温度')
    ax3.set_xlabel('起始温度 (°C)')
    ax3.set_ylabel('结束温度 (°C)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 添加对角线参考
    min_temp = min(min(start_temps), min(end_temps))
    max_temp = max(max(start_temps), max(end_temps))
    ax3.plot([min_temp, max_temp], [min_temp, max_temp], 
             'k--', alpha=0.5, label='起始=结束线')
    
    # 4. 温度上升分布（右下）
    ax4 = axes[1, 1]
    
    temp_rises = [stats['temp_rise'] for stats in sample_stats.values()]
    sample_ids = list(sample_stats.keys())
    
    # 创建柱状图
    colors_bar = ['red' if sid in excluded_samples else 'blue' for sid in sample_ids]
    bars = ax4.bar(range(len(sample_ids)), temp_rises, color=colors_bar, alpha=0.7)
    
    ax4.set_title('各样本温度上升幅度')
    ax4.set_xlabel('样本编号')
    ax4.set_ylabel('温度上升 (°C)')
    ax4.set_xticks(range(len(sample_ids)))
    ax4.set_xticklabels([f'S{sid}' for sid in sample_ids], rotation=45)
    ax4.grid(True, alpha=0.3, axis='y')
    
    # 添加平均线
    mean_rise = np.mean(temp_rises)
    ax4.axhline(y=mean_rise, color='green', linestyle='--', 
                label=f'平均上升: {mean_rise:.1f}°C')
    ax4.legend()
    
    # 在柱状图上添加数值
    for i, (bar, rise) in enumerate(zip(bars, temp_rises)):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{rise:.1f}', ha='center', va='bottom', fontsize=8)
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = f"results/all_samples_overview_{timestamp}.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"总览图表已保存: {output_path}")
    
    plt.show()

def create_detailed_analysis_plots(samples_data, sample_stats):
    """创建详细分析图表"""
    
    # 创建分组显示图表
    fig, axes = plt.subplots(3, 3, figsize=(24, 18))
    fig.suptitle('酯化反应温度序列 - 分组详细分析', fontsize=18, fontweight='bold')
    
    # 将样本分为9组，每组2-3个样本
    sample_groups = [
        [1, 2, 3], [4, 5, 6], [7, 8, 9],
        [10, 11, 12], [13, 14, 15], [16, 17, 18],
        [19, 20, 21], [], []  # 最后两组留空用于其他分析
    ]
    
    colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive']
    
    for group_idx, sample_group in enumerate(sample_groups[:7]):
        row = group_idx // 3
        col = group_idx % 3
        ax = axes[row, col]
        
        for i, sample_id in enumerate(sample_group):
            if sample_id in samples_data:
                temp_data = samples_data[sample_id]
                
                # 重采样以提高显示性能
                if len(temp_data) > 2000:
                    indices = np.linspace(0, len(temp_data)-1, 2000).astype(int)
                    temp_resampled = temp_data[indices]
                    time_points = np.linspace(0, len(temp_data)-1, 2000)
                else:
                    temp_resampled = temp_data
                    time_points = np.arange(len(temp_data))
                
                # 标记排除的样本
                if sample_id in [8, 13, 19, 20]:
                    ax.plot(time_points, temp_resampled, 
                           label=f'Sample_{sample_id} (排除)', 
                           color=colors[i], linestyle='--', alpha=0.6, linewidth=2)
                else:
                    ax.plot(time_points, temp_resampled, 
                           label=f'Sample_{sample_id}', 
                           color=colors[i], alpha=0.8, linewidth=2)
        
        ax.set_title(f'样本组 {group_idx + 1}: {sample_group}')
        ax.set_xlabel('时间点')
        ax.set_ylabel('温度 (°C)')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    # 第8个子图：温度变化率分析
    ax8 = axes[2, 1]
    
    # 计算并显示几个代表性样本的温度变化率
    representative_samples = [1, 5, 10, 15, 21]
    for i, sample_id in enumerate(representative_samples):
        if sample_id in samples_data:
            temp_data = samples_data[sample_id]
            changes = np.diff(temp_data)
            
            # 计算移动平均以平滑变化率
            window_size = max(1, len(changes) // 100)
            if len(changes) > window_size:
                smooth_changes = np.convolve(changes, np.ones(window_size)/window_size, mode='valid')
                time_points = np.arange(len(smooth_changes))
                ax8.plot(time_points, smooth_changes, 
                        label=f'Sample_{sample_id}', 
                        color=colors[i], alpha=0.8, linewidth=2)
    
    ax8.set_title('温度变化率对比')
    ax8.set_xlabel('时间点')
    ax8.set_ylabel('温度变化率 (°C/点)')
    ax8.legend()
    ax8.grid(True, alpha=0.3)
    ax8.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    # 第9个子图：数据长度分布
    ax9 = axes[2, 2]
    
    lengths = [stats['length'] for stats in sample_stats.values()]
    sample_ids = list(sample_stats.keys())
    
    colors_bar = ['red' if sid in [8, 13, 19, 20] else 'blue' for sid in sample_ids]
    bars = ax9.bar(range(len(sample_ids)), lengths, color=colors_bar, alpha=0.7)
    
    ax9.set_title('各样本数据长度')
    ax9.set_xlabel('样本编号')
    ax9.set_ylabel('数据点数')
    ax9.set_xticks(range(len(sample_ids)))
    ax9.set_xticklabels([f'S{sid}' for sid in sample_ids], rotation=45)
    ax9.grid(True, alpha=0.3, axis='y')
    
    # 添加平均线
    mean_length = np.mean(lengths)
    ax9.axhline(y=mean_length, color='green', linestyle='--', 
                label=f'平均长度: {mean_length:,.0f}')
    ax9.legend()
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = f"results/all_samples_detailed_{timestamp}.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"详细分析图表已保存: {output_path}")
    
    plt.show()

def create_statistics_plots(sample_stats):
    """创建统计分析图表"""
    
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    fig.suptitle('酯化反应温度序列 - 统计特征分析', fontsize=16, fontweight='bold')
    
    sample_ids = list(sample_stats.keys())
    excluded_samples = [8, 13, 19, 20]
    
    # 1. 起始温度分布
    ax1 = axes[0, 0]
    start_temps = [stats['start_temp'] for stats in sample_stats.values()]
    colors = ['red' if sid in excluded_samples else 'blue' for sid in sample_ids]
    
    bars = ax1.bar(range(len(sample_ids)), start_temps, color=colors, alpha=0.7)
    ax1.set_title('起始温度分布')
    ax1.set_xlabel('样本编号')
    ax1.set_ylabel('起始温度 (°C)')
    ax1.set_xticks(range(len(sample_ids)))
    ax1.set_xticklabels([f'S{sid}' for sid in sample_ids], rotation=45)
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 2. 最高温度分布
    ax2 = axes[0, 1]
    max_temps = [stats['max_temp'] for stats in sample_stats.values()]
    
    bars = ax2.bar(range(len(sample_ids)), max_temps, color=colors, alpha=0.7)
    ax2.set_title('最高温度分布')
    ax2.set_xlabel('样本编号')
    ax2.set_ylabel('最高温度 (°C)')
    ax2.set_xticks(range(len(sample_ids)))
    ax2.set_xticklabels([f'S{sid}' for sid in sample_ids], rotation=45)
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 3. 平均温度分布
    ax3 = axes[0, 2]
    mean_temps = [stats['mean_temp'] for stats in sample_stats.values()]
    
    bars = ax3.bar(range(len(sample_ids)), mean_temps, color=colors, alpha=0.7)
    ax3.set_title('平均温度分布')
    ax3.set_xlabel('样本编号')
    ax3.set_ylabel('平均温度 (°C)')
    ax3.set_xticks(range(len(sample_ids)))
    ax3.set_xticklabels([f'S{sid}' for sid in sample_ids], rotation=45)
    ax3.grid(True, alpha=0.3, axis='y')
    
    # 4. 温度标准差分布
    ax4 = axes[1, 0]
    std_temps = [stats['std_temp'] for stats in sample_stats.values()]
    
    bars = ax4.bar(range(len(sample_ids)), std_temps, color=colors, alpha=0.7)
    ax4.set_title('温度标准差分布')
    ax4.set_xlabel('样本编号')
    ax4.set_ylabel('温度标准差 (°C)')
    ax4.set_xticks(range(len(sample_ids)))
    ax4.set_xticklabels([f'S{sid}' for sid in sample_ids], rotation=45)
    ax4.grid(True, alpha=0.3, axis='y')
    
    # 5. 温度上升vs数据长度散点图
    ax5 = axes[1, 1]
    temp_rises = [stats['temp_rise'] for stats in sample_stats.values()]
    lengths = [stats['length'] for stats in sample_stats.values()]
    
    for i, sample_id in enumerate(sample_ids):
        if sample_id in excluded_samples:
            ax5.scatter(lengths[i], temp_rises[i], 
                       s=100, alpha=0.6, marker='x', color='red',
                       label='排除样本' if sample_id == excluded_samples[0] else "")
        else:
            ax5.scatter(lengths[i], temp_rises[i], 
                       s=100, alpha=0.8, marker='o', color='blue',
                       label='使用样本' if sample_id == 1 else "")
        
        ax5.annotate(f'S{sample_id}', 
                    (lengths[i], temp_rises[i]), 
                    xytext=(5, 5), textcoords='offset points',
                    fontsize=8, alpha=0.8)
    
    ax5.set_title('数据长度 vs 温度上升')
    ax5.set_xlabel('数据点数')
    ax5.set_ylabel('温度上升 (°C)')
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    
    # 6. 综合统计表格
    ax6 = axes[1, 2]
    ax6.axis('off')
    
    # 计算统计摘要
    used_samples = [sid for sid in sample_ids if sid not in excluded_samples]
    used_stats = [sample_stats[sid] for sid in used_samples]
    
    summary_data = {
        '指标': ['样本数量', '平均起始温度', '平均结束温度', '平均温度上升', 
                '平均最高温度', '平均数据长度', '温度上升标准差'],
        '使用样本': [
            len(used_samples),
            f"{np.mean([s['start_temp'] for s in used_stats]):.1f}°C",
            f"{np.mean([s['end_temp'] for s in used_stats]):.1f}°C", 
            f"{np.mean([s['temp_rise'] for s in used_stats]):.1f}°C",
            f"{np.mean([s['max_temp'] for s in used_stats]):.1f}°C",
            f"{np.mean([s['length'] for s in used_stats]):,.0f}",
            f"{np.std([s['temp_rise'] for s in used_stats]):.1f}°C"
        ],
        '全部样本': [
            len(sample_ids),
            f"{np.mean([s['start_temp'] for s in sample_stats.values()]):.1f}°C",
            f"{np.mean([s['end_temp'] for s in sample_stats.values()]):.1f}°C",
            f"{np.mean([s['temp_rise'] for s in sample_stats.values()]):.1f}°C", 
            f"{np.mean([s['max_temp'] for s in sample_stats.values()]):.1f}°C",
            f"{np.mean([s['length'] for s in sample_stats.values()]):,.0f}",
            f"{np.std([s['temp_rise'] for s in sample_stats.values()]):.1f}°C"
        ]
    }
    
    # 创建表格
    table_data = []
    for i in range(len(summary_data['指标'])):
        table_data.append([summary_data['指标'][i], 
                          summary_data['使用样本'][i], 
                          summary_data['全部样本'][i]])
    
    table = ax6.table(cellText=table_data,
                     colLabels=['统计指标', '使用样本(17个)', '全部样本(21个)'],
                     cellLoc='center',
                     loc='center',
                     bbox=[0, 0, 1, 1])
    
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 2)
    
    # 设置表格样式
    for i in range(len(table_data) + 1):
        for j in range(3):
            cell = table[(i, j)]
            if i == 0:  # 表头
                cell.set_facecolor('#4CAF50')
                cell.set_text_props(weight='bold', color='white')
            else:
                cell.set_facecolor('#f0f0f0' if i % 2 == 0 else 'white')
    
    ax6.set_title('统计摘要', fontsize=14, fontweight='bold', pad=20)
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = f"results/all_samples_statistics_{timestamp}.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"统计分析图表已保存: {output_path}")
    
    plt.show()

if __name__ == "__main__":
    print("开始绘制所有21个样本的温度曲线图...")
    plot_all_samples()
    print("绘制完成！")
