#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终综合对比分析
对比原始模拟、改进PSO和真实数据的效果
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import glob
import os
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_all_data():
    """加载所有数据"""
    print("加载数据...")
    
    # 1. 加载真实数据
    real_data = {}
    real_data_dir = "data/Esterification/"
    sample_files = glob.glob(os.path.join(real_data_dir, "Sample_*.xlsx"))
    
    for file_path in sample_files[:5]:  # 只加载前5个作为代表
        file_name = os.path.basename(file_path)
        sample_num = file_name.replace('Sample_', '').replace('.xlsx', '')
        
        try:
            df = pd.read_excel(file_path, sheet_name=0)
            df.columns = [col.strip().replace('\t', '') for col in df.columns]
            for col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            if len(df.columns) > 0:
                temp_data = df.iloc[:, 0].dropna()
                if len(temp_data) > 0:
                    real_data[f"Sample_{sample_num}"] = temp_data.values
                    print(f"  真实数据 {file_name}: {len(temp_data)} 个数据点")
        except Exception as e:
            print(f"  {file_name}: 读取失败 - {e}")
    
    # 2. 加载原始模拟数据
    original_sim_file = "results/advanced_temperature_sequence_20250724_212347.csv"
    try:
        df_original = pd.read_csv(original_sim_file)
        original_data = df_original['温度(°C)'].values
        print(f"  原始模拟数据: {len(original_data)} 个数据点")
    except Exception as e:
        print(f"  原始模拟数据加载失败: {e}")
        original_data = None
    
    # 3. 加载改进PSO数据
    improved_pso_file = "results/improved_pso_temperature_sequence_20250724_222016.csv"
    try:
        df_improved = pd.read_csv(improved_pso_file)
        improved_data = df_improved['温度(°C)'].values
        print(f"  改进PSO数据: {len(improved_data)} 个数据点")
    except Exception as e:
        print(f"  改进PSO数据加载失败: {e}")
        improved_data = None
    
    return real_data, original_data, improved_data

def calculate_comprehensive_metrics(data, name):
    """计算综合评估指标"""
    if data is None or len(data) == 0:
        return {}
    
    n_points = len(data)
    early_idx = n_points // 3
    mid_idx = 2 * n_points // 3
    
    # 基本统计
    metrics = {
        'name': name,
        'length': n_points,
        'min_temp': data.min(),
        'max_temp': data.max(),
        'temp_range': data.max() - data.min(),
        'mean_temp': np.mean(data),
        'final_temp': data[-1],
        'initial_temp': data[0]
    }
    
    # 变化率分析
    diff_data = np.diff(data)
    metrics['avg_change_rate'] = np.mean(diff_data)
    metrics['std_change_rate'] = np.std(diff_data)
    metrics['max_increase'] = np.max(diff_data)
    metrics['max_decrease'] = np.min(diff_data)
    
    # 阶段分析
    early_data = data[:early_idx]
    mid_data = data[early_idx:mid_idx]
    late_data = data[mid_idx:]
    
    # 计算各阶段斜率
    def calc_slope(segment, start_idx=0):
        if len(segment) < 2:
            return 0, 0
        x = np.arange(len(segment)) + start_idx
        slope, intercept, r_value, p_value, std_err = stats.linregress(x, segment)
        return slope, r_value
    
    metrics['early_slope'], metrics['early_r2'] = calc_slope(early_data, 0)
    metrics['mid_slope'], metrics['mid_r2'] = calc_slope(mid_data, early_idx)
    metrics['late_slope'], metrics['late_r2'] = calc_slope(late_data, mid_idx)
    
    # 稳定性分析
    metrics['late_stability'] = np.std(late_data)
    metrics['overall_stability'] = np.std(data)
    
    # 平滑性分析
    second_diff = np.diff(data, n=2)
    metrics['smoothness'] = 1.0 / (1.0 + np.std(second_diff))
    
    return metrics

def create_comprehensive_comparison_plot(real_data, original_data, improved_data):
    """创建综合对比图表"""
    print("生成综合对比图表...")
    
    fig, axes = plt.subplots(3, 3, figsize=(20, 15))
    fig.suptitle('原始模拟 vs 改进PSO vs 真实数据 - 综合对比分析', fontsize=16, fontweight='bold')
    
    # 统一长度处理
    min_length = 15000  # 设置一个合理的对比长度
    
    # 1. 温度曲线对比 (左上)
    ax1 = axes[0, 0]
    
    # 绘制真实数据样本
    colors = ['lightblue', 'lightgreen', 'lightcoral', 'lightyellow', 'lightpink']
    for i, (sample_name, temp_data) in enumerate(list(real_data.items())[:5]):
        if len(temp_data) >= min_length:
            ax1.plot(temp_data[:min_length], color=colors[i], alpha=0.6, linewidth=1, label=f'{sample_name}')
    
    # 绘制模拟数据
    if original_data is not None and len(original_data) >= min_length:
        ax1.plot(original_data[:min_length], 'r--', linewidth=2, label='原始模拟', alpha=0.8)
    
    if improved_data is not None and len(improved_data) >= min_length:
        ax1.plot(improved_data[:min_length], 'b-', linewidth=2, label='改进PSO', alpha=0.8)
    
    ax1.set_title('温度序列对比', fontsize=12, fontweight='bold')
    ax1.set_xlabel('时间点')
    ax1.set_ylabel('温度 (°C)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 变化率分布对比 (中上)
    ax2 = axes[0, 1]
    
    # 计算变化率
    if original_data is not None:
        original_diff = np.diff(original_data[:min_length])
        ax2.hist(original_diff, bins=50, alpha=0.7, color='red', density=True, label='原始模拟')
    
    if improved_data is not None:
        improved_diff = np.diff(improved_data[:min_length])
        ax2.hist(improved_diff, bins=50, alpha=0.7, color='blue', density=True, label='改进PSO')
    
    # 真实数据变化率
    all_real_diffs = []
    for temp_data in real_data.values():
        if len(temp_data) >= min_length:
            all_real_diffs.extend(np.diff(temp_data[:min_length]))
    
    if all_real_diffs:
        ax2.hist(all_real_diffs, bins=50, alpha=0.5, color='green', density=True, label='真实数据')
    
    ax2.set_title('变化率分布对比', fontsize=12, fontweight='bold')
    ax2.set_xlabel('变化率 (°C/步)')
    ax2.set_ylabel('密度')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 后期稳定性对比 (右上)
    ax3 = axes[0, 2]
    
    late_start = 2 * min_length // 3
    
    # 计算后期稳定性
    stabilities = {'真实数据': [], '原始模拟': [], '改进PSO': []}
    
    for temp_data in real_data.values():
        if len(temp_data) >= min_length:
            late_data = temp_data[late_start:min_length]
            stabilities['真实数据'].append(np.std(late_data))
    
    if original_data is not None and len(original_data) >= min_length:
        late_data = original_data[late_start:min_length]
        stabilities['原始模拟'].append(np.std(late_data))
    
    if improved_data is not None and len(improved_data) >= min_length:
        late_data = improved_data[late_start:min_length]
        stabilities['改进PSO'].append(np.std(late_data))
    
    # 绘制箱线图
    data_to_plot = [stabilities['真实数据'], stabilities['原始模拟'], stabilities['改进PSO']]
    labels = ['真实数据', '原始模拟', '改进PSO']
    
    bp = ax3.boxplot([d for d in data_to_plot if d], labels=[l for l, d in zip(labels, data_to_plot) if d])
    ax3.set_title('后期稳定性对比', fontsize=12, fontweight='bold')
    ax3.set_ylabel('标准差 (°C)')
    ax3.grid(True, alpha=0.3)
    
    # 4. 阶段斜率对比 (左中)
    ax4 = axes[1, 0]
    
    # 计算各方法的阶段斜率
    methods = ['真实数据', '原始模拟', '改进PSO']
    early_slopes = []
    late_slopes = []
    
    # 真实数据
    real_early_slopes = []
    real_late_slopes = []
    for temp_data in real_data.values():
        if len(temp_data) >= min_length:
            data_truncated = temp_data[:min_length]
            early_idx = len(data_truncated) // 3
            late_idx = 2 * len(data_truncated) // 3
            
            early_slope = stats.linregress(np.arange(early_idx), data_truncated[:early_idx])[0]
            late_slope = stats.linregress(np.arange(len(data_truncated) - late_idx), data_truncated[late_idx:])[0]
            
            real_early_slopes.append(early_slope)
            real_late_slopes.append(late_slope)
    
    early_slopes.append(np.mean(real_early_slopes) if real_early_slopes else 0)
    late_slopes.append(np.mean(real_late_slopes) if real_late_slopes else 0)
    
    # 原始模拟
    if original_data is not None and len(original_data) >= min_length:
        data_truncated = original_data[:min_length]
        early_idx = len(data_truncated) // 3
        late_idx = 2 * len(data_truncated) // 3
        
        early_slope = stats.linregress(np.arange(early_idx), data_truncated[:early_idx])[0]
        late_slope = stats.linregress(np.arange(len(data_truncated) - late_idx), data_truncated[late_idx:])[0]
        
        early_slopes.append(early_slope)
        late_slopes.append(late_slope)
    else:
        early_slopes.append(0)
        late_slopes.append(0)
    
    # 改进PSO
    if improved_data is not None and len(improved_data) >= min_length:
        data_truncated = improved_data[:min_length]
        early_idx = len(data_truncated) // 3
        late_idx = 2 * len(data_truncated) // 3
        
        early_slope = stats.linregress(np.arange(early_idx), data_truncated[:early_idx])[0]
        late_slope = stats.linregress(np.arange(len(data_truncated) - late_idx), data_truncated[late_idx:])[0]
        
        early_slopes.append(early_slope)
        late_slopes.append(late_slope)
    else:
        early_slopes.append(0)
        late_slopes.append(0)
    
    x = np.arange(len(methods))
    width = 0.35
    
    ax4.bar(x - width/2, early_slopes, width, label='初期斜率', alpha=0.8)
    ax4.bar(x + width/2, late_slopes, width, label='后期斜率', alpha=0.8)
    
    ax4.set_title('阶段斜率对比', fontsize=12, fontweight='bold')
    ax4.set_xlabel('方法')
    ax4.set_ylabel('斜率')
    ax4.set_xticks(x)
    ax4.set_xticklabels(methods)
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 5. 最终温度对比 (中中)
    ax5 = axes[1, 1]
    
    final_temps = []
    
    # 真实数据最终温度
    real_final_temps = [temp_data[-1] for temp_data in real_data.values() if len(temp_data) > 0]
    final_temps.append(np.mean(real_final_temps) if real_final_temps else 0)
    
    # 原始模拟最终温度
    if original_data is not None and len(original_data) > 0:
        final_temps.append(original_data[min_length-1])
    else:
        final_temps.append(0)
    
    # 改进PSO最终温度
    if improved_data is not None and len(improved_data) > 0:
        final_temps.append(improved_data[min_length-1])
    else:
        final_temps.append(0)
    
    colors = ['green', 'red', 'blue']
    bars = ax5.bar(methods, final_temps, color=colors, alpha=0.7)
    
    # 添加数值标签
    for bar, temp in zip(bars, final_temps):
        height = bar.get_height()
        ax5.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{temp:.1f}°C', ha='center', va='bottom')
    
    ax5.set_title('最终温度对比', fontsize=12, fontweight='bold')
    ax5.set_ylabel('温度 (°C)')
    ax5.grid(True, alpha=0.3)
    
    # 6. 综合评分雷达图 (右中)
    ax6 = axes[1, 2]
    
    # 计算综合评分（基于与真实数据的接近程度）
    real_metrics = calculate_comprehensive_metrics(list(real_data.values())[0], "真实数据")
    
    scores = {'原始模拟': [], '改进PSO': []}
    metric_names = ['变化率', '后期斜率', '最终温度', '稳定性']
    
    if original_data is not None:
        original_metrics = calculate_comprehensive_metrics(original_data, "原始模拟")
        # 计算相对误差并转换为分数
        change_rate_score = max(0, 1 - abs(original_metrics['avg_change_rate'] - real_metrics['avg_change_rate']) / abs(real_metrics['avg_change_rate']))
        late_slope_score = max(0, 1 - abs(original_metrics['late_slope'] - real_metrics['late_slope']) / abs(real_metrics['late_slope']) if real_metrics['late_slope'] != 0 else 0.5)
        final_temp_score = max(0, 1 - abs(original_metrics['final_temp'] - real_metrics['final_temp']) / abs(real_metrics['final_temp']))
        stability_score = max(0, 1 - abs(original_metrics['late_stability'] - real_metrics['late_stability']) / abs(real_metrics['late_stability']))
        
        scores['原始模拟'] = [change_rate_score, late_slope_score, final_temp_score, stability_score]
    
    if improved_data is not None:
        improved_metrics = calculate_comprehensive_metrics(improved_data, "改进PSO")
        # 计算相对误差并转换为分数
        change_rate_score = max(0, 1 - abs(improved_metrics['avg_change_rate'] - real_metrics['avg_change_rate']) / abs(real_metrics['avg_change_rate']))
        late_slope_score = max(0, 1 - abs(improved_metrics['late_slope'] - real_metrics['late_slope']) / abs(real_metrics['late_slope']) if real_metrics['late_slope'] != 0 else 0.5)
        final_temp_score = max(0, 1 - abs(improved_metrics['final_temp'] - real_metrics['final_temp']) / abs(real_metrics['final_temp']))
        stability_score = max(0, 1 - abs(improved_metrics['late_stability'] - real_metrics['late_stability']) / abs(real_metrics['late_stability']))
        
        scores['改进PSO'] = [change_rate_score, late_slope_score, final_temp_score, stability_score]
    
    # 绘制雷达图
    x = np.arange(len(metric_names))
    width = 0.35
    
    if scores['原始模拟']:
        ax6.bar(x - width/2, scores['原始模拟'], width, label='原始模拟', alpha=0.8, color='red')
    if scores['改进PSO']:
        ax6.bar(x + width/2, scores['改进PSO'], width, label='改进PSO', alpha=0.8, color='blue')
    
    ax6.set_title('综合评分对比', fontsize=12, fontweight='bold')
    ax6.set_xlabel('评估指标')
    ax6.set_ylabel('相似度分数')
    ax6.set_xticks(x)
    ax6.set_xticklabels(metric_names, rotation=45)
    ax6.legend()
    ax6.grid(True, alpha=0.3)
    ax6.set_ylim(0, 1)
    
    # 7-9. 详细统计对比表格
    for i, ax in enumerate([axes[2, 0], axes[2, 1], axes[2, 2]]):
        ax.axis('off')
    
    # 合并底部三个子图用于显示统计表格
    ax_table = fig.add_subplot(3, 1, 3)
    ax_table.axis('off')
    
    # 创建对比表格
    table_data = []
    headers = ['指标', '真实数据均值', '原始模拟', '改进PSO', '原始误差', '改进误差', '改进幅度']
    
    # 计算平均真实数据指标
    real_avg_metrics = {}
    for key in ['avg_change_rate', 'late_slope', 'final_temp', 'late_stability']:
        values = []
        for temp_data in real_data.values():
            if len(temp_data) >= min_length:
                metrics = calculate_comprehensive_metrics(temp_data[:min_length], "temp")
                values.append(metrics[key])
        real_avg_metrics[key] = np.mean(values) if values else 0
    
    # 添加表格行
    metrics_to_show = [
        ('平均变化率', 'avg_change_rate', '{:.6f}'),
        ('后期斜率', 'late_slope', '{:.6f}'),
        ('最终温度(°C)', 'final_temp', '{:.2f}'),
        ('后期稳定性', 'late_stability', '{:.3f}')
    ]
    
    for metric_name, metric_key, format_str in metrics_to_show:
        real_val = real_avg_metrics[metric_key]
        
        if original_data is not None:
            orig_metrics = calculate_comprehensive_metrics(original_data[:min_length], "original")
            orig_val = orig_metrics[metric_key]
            orig_error = abs(orig_val - real_val)
        else:
            orig_val = 0
            orig_error = 0
        
        if improved_data is not None:
            imp_metrics = calculate_comprehensive_metrics(improved_data[:min_length], "improved")
            imp_val = imp_metrics[metric_key]
            imp_error = abs(imp_val - real_val)
        else:
            imp_val = 0
            imp_error = 0
        
        improvement = ((orig_error - imp_error) / orig_error * 100) if orig_error != 0 else 0
        
        row = [
            metric_name,
            format_str.format(real_val),
            format_str.format(orig_val),
            format_str.format(imp_val),
            format_str.format(orig_error),
            format_str.format(imp_error),
            f'{improvement:.1f}%'
        ]
        table_data.append(row)
    
    # 绘制表格
    table = ax_table.table(cellText=table_data, colLabels=headers, 
                          cellLoc='center', loc='center',
                          bbox=[0, 0, 1, 1])
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 2)
    
    # 设置表格样式
    for i in range(len(headers)):
        table[(0, i)].set_facecolor('#40466e')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    # 为改进幅度列添加颜色
    for i in range(1, len(table_data) + 1):
        improvement_val = float(table_data[i-1][-1].replace('%', ''))
        col_idx = len(headers) - 1  # 最后一列的索引
        if improvement_val > 50:
            table[(i, col_idx)].set_facecolor('#90EE90')  # 浅绿色
        elif improvement_val > 0:
            table[(i, col_idx)].set_facecolor('#FFFFE0')  # 浅黄色
        else:
            table[(i, col_idx)].set_facecolor('#FFB6C1')  # 浅红色
    
    plt.tight_layout()
    
    # 保存图片
    output_file = "results/final_comprehensive_comparison.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"综合对比图表已保存至: {output_file}")
    plt.show()
    
    return output_file

def main():
    """主函数"""
    print("最终综合对比分析")
    print("=" * 50)
    
    # 加载所有数据
    real_data, original_data, improved_data = load_all_data()
    
    if not real_data:
        print("错误: 无法加载真实数据")
        return
    
    # 生成综合对比图表
    print("\n生成综合对比分析...")
    plot_file = create_comprehensive_comparison_plot(real_data, original_data, improved_data)
    
    print("\n" + "=" * 50)
    print("综合对比分析完成！")
    print(f"生成的图表: {plot_file}")
    print("=" * 50)

if __name__ == "__main__":
    main()
