#!/usr/bin/env python3
"""
工具函数模块

提供系统通用的工具函数，包括：
- 配置文件加载
- 日志设置
- 目录创建
- 数据验证
"""

import os
import yaml
import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime


def load_config(config_path: str = "config/config.yaml") -> Dict[str, Any]:
    """
    加载配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置字典
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        raise ValueError(f"加载配置文件失败: {e}")


def setup_logging(config: Dict[str, Any]) -> logging.Logger:
    """
    设置日志系统
    
    Args:
        config: 配置字典
        
    Returns:
        配置好的logger
    """
    log_config = config.get('logging', {})
    
    # 创建logger
    logger = logging.getLogger('optimization_system')
    logger.setLevel(getattr(logging, log_config.get('level', 'INFO')))
    
    # 清除现有handlers
    logger.handlers.clear()
    
    # 设置格式
    formatter = logging.Formatter(
        log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    )
    
    # 控制台输出
    if log_config.get('console', True):
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # 文件输出
    if 'file' in log_config:
        file_handler = logging.FileHandler(log_config['file'], encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def create_directories(directories: List[str]):
    """
    创建目录结构
    
    Args:
        directories: 目录路径列表
    """
    for directory in directories:
        os.makedirs(directory, exist_ok=True)


def validate_temperature_sequence(sequence: np.ndarray, 
                                min_temp: float = 0.0, 
                                max_temp: float = 200.0) -> bool:
    """
    验证温度序列的有效性
    
    Args:
        sequence: 温度序列
        min_temp: 最低温度
        max_temp: 最高温度
        
    Returns:
        是否有效
    """
    if len(sequence) == 0:
        return False
    
    # 检查温度范围
    if np.any(sequence < min_temp) or np.any(sequence > max_temp):
        return False
    
    # 检查是否包含NaN或无穷值
    if np.any(np.isnan(sequence)) or np.any(np.isinf(sequence)):
        return False
    
    return True


def calculate_sequence_similarity(seq1: np.ndarray, seq2: np.ndarray) -> float:
    """
    计算两个序列的相似度
    
    Args:
        seq1: 第一个序列
        seq2: 第二个序列
        
    Returns:
        相似度分数 (0-1)
    """
    # 标准化序列长度
    min_len = min(len(seq1), len(seq2))
    seq1_norm = seq1[:min_len]
    seq2_norm = seq2[:min_len]
    
    # 计算皮尔逊相关系数
    correlation = np.corrcoef(seq1_norm, seq2_norm)[0, 1]
    
    # 处理NaN情况
    if np.isnan(correlation):
        correlation = 0.0
    
    # 转换为相似度分数
    similarity = (correlation + 1) / 2
    
    return similarity


def downsample_sequence(sequence: np.ndarray, target_length: int) -> np.ndarray:
    """
    下采样序列到目标长度
    
    Args:
        sequence: 原始序列
        target_length: 目标长度
        
    Returns:
        下采样后的序列
    """
    if len(sequence) <= target_length:
        return sequence
    
    # 等间隔采样
    indices = np.linspace(0, len(sequence) - 1, target_length, dtype=int)
    return sequence[indices]


def normalize_sequence(sequence: np.ndarray, method: str = 'minmax') -> np.ndarray:
    """
    标准化序列
    
    Args:
        sequence: 输入序列
        method: 标准化方法 ('minmax', 'zscore')
        
    Returns:
        标准化后的序列
    """
    if method == 'minmax':
        min_val = np.min(sequence)
        max_val = np.max(sequence)
        if max_val > min_val:
            return (sequence - min_val) / (max_val - min_val)
        else:
            return np.zeros_like(sequence)
    
    elif method == 'zscore':
        mean_val = np.mean(sequence)
        std_val = np.std(sequence)
        if std_val > 0:
            return (sequence - mean_val) / std_val
        else:
            return np.zeros_like(sequence)
    
    else:
        raise ValueError(f"不支持的标准化方法: {method}")


def generate_timestamp() -> str:
    """
    生成时间戳字符串
    
    Returns:
        格式化的时间戳
    """
    return datetime.now().strftime("%Y%m%d_%H%M%S")


def save_results(results: Dict[str, Any], output_path: str):
    """
    保存结果到文件
    
    Args:
        results: 结果字典
        output_path: 输出路径
    """
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 添加时间戳
    results['timestamp'] = generate_timestamp()
    
    # 保存为numpy格式
    np.save(output_path, results)


def load_results(input_path: str) -> Dict[str, Any]:
    """
    从文件加载结果
    
    Args:
        input_path: 输入路径
        
    Returns:
        结果字典
    """
    return np.load(input_path, allow_pickle=True).item()


def print_system_info():
    """打印系统信息"""
    print("=" * 50)
    print("化工车间温度序列PSO优化系统")
    print("=" * 50)
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {os.sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print("=" * 50)


def validate_config(config: Dict[str, Any]) -> bool:
    """
    验证配置文件的完整性

    Args:
        config: 配置字典

    Returns:
        是否有效
    """
    required_sections = ['data', 'feature_extraction', 'classifier', 'pso', 'model']

    for section in required_sections:
        if section not in config:
            print(f"配置文件缺少必需的部分: {section}")
            return False

    return True


def load_fixed_prefix_from_excel(file_path: str) -> Tuple[np.ndarray, Dict[str, Any]]:
    """
    从Excel文件加载固定前缀温度序列

    Args:
        file_path: Excel文件路径

    Returns:
        (固定前缀数组, 元数据字典) 元组

    Raises:
        FileNotFoundError: 文件不存在
        ValueError: 文件格式错误或数据无效
    """
    logger = logging.getLogger('optimization_system')

    # 检查文件是否存在
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"固定前缀文件不存在: {file_path}")

    # 检查文件扩展名
    file_ext = os.path.splitext(file_path)[1].lower()
    if file_ext not in ['.xlsx', '.xls']:
        raise ValueError(f"不支持的文件格式: {file_ext}，仅支持 .xlsx 和 .xls 格式")

    try:
        # 读取Excel文件（不使用第一行作为标题）
        logger.info(f"正在读取固定前缀文件: {file_path}")
        df = pd.read_excel(file_path, header=None)

        # 验证数据结构
        if df.empty:
            raise ValueError("Excel文件为空")

        if df.shape[1] != 1:
            raise ValueError(f"Excel文件应包含单列数据，但发现 {df.shape[1]} 列")

        # 提取数据列（取第一列）
        data_column = df.iloc[:, 0]

        # 检查数据类型和有效性
        if not pd.api.types.is_numeric_dtype(data_column):
            # 尝试转换为数值类型
            try:
                data_column = pd.to_numeric(data_column, errors='coerce')
            except Exception:
                raise ValueError("Excel文件中包含非数值数据，无法转换为温度序列")

        # 检查是否有缺失值
        if data_column.isnull().any():
            null_count = data_column.isnull().sum()
            raise ValueError(f"Excel文件中包含 {null_count} 个缺失值，请检查数据完整性")

        # 转换为numpy数组
        fixed_prefix = data_column.values.astype(np.float64)

        # 基本数据验证
        if len(fixed_prefix) == 0:
            raise ValueError("固定前缀序列为空")

        # 温度范围验证（宽松检查）
        min_temp, max_temp = np.min(fixed_prefix), np.max(fixed_prefix)
        if min_temp < -50 or max_temp > 300:
            logger.warning(f"固定前缀温度范围异常: [{min_temp:.2f}, {max_temp:.2f}]°C")

        # 检查异常值
        if np.any(np.isnan(fixed_prefix)) or np.any(np.isinf(fixed_prefix)):
            raise ValueError("固定前缀序列包含NaN或无穷值")

        # 生成元数据
        metadata = {
            'file_path': file_path,
            'length': len(fixed_prefix),
            'min_temp': float(min_temp),
            'max_temp': float(max_temp),
            'mean_temp': float(np.mean(fixed_prefix)),
            'std_temp': float(np.std(fixed_prefix)),
            'load_time': datetime.now().isoformat()
        }

        logger.info(f"成功加载固定前缀序列:")
        logger.info(f"  长度: {metadata['length']:,} 个数据点")
        logger.info(f"  温度范围: [{metadata['min_temp']:.2f}, {metadata['max_temp']:.2f}]°C")
        logger.info(f"  平均温度: {metadata['mean_temp']:.2f}°C")

        return fixed_prefix, metadata

    except Exception as e:
        # 处理各种可能的Excel读取错误
        if "Excel" in str(e) or "xlrd" in str(e) or "openpyxl" in str(e):
            raise ValueError(f"Excel文件读取失败: {e}")
        else:
            raise ValueError(f"加载固定前缀序列时发生错误: {e}")


def validate_fixed_prefix_compatibility(fixed_prefix: np.ndarray,
                                       pso_config: Dict[str, Any]) -> Tuple[bool, str]:
    """
    验证固定前缀与PSO配置的兼容性

    Args:
        fixed_prefix: 固定前缀数组
        pso_config: PSO配置字典

    Returns:
        (是否兼容, 错误信息) 元组
    """
    try:
        # 获取PSO温度配置
        temp_config = pso_config.get('temperature_sequence', {})
        min_temp = temp_config.get('min_temp', 0)
        max_temp = temp_config.get('max_temp', 200)

        # 检查温度范围兼容性
        prefix_min = np.min(fixed_prefix)
        prefix_max = np.max(fixed_prefix)

        if prefix_min < min_temp or prefix_max > max_temp:
            return False, (f"固定前缀温度范围 [{prefix_min:.2f}, {prefix_max:.2f}]°C "
                          f"超出PSO配置范围 [{min_temp}, {max_temp}]°C")

        # 检查序列长度合理性
        prefix_length = len(fixed_prefix)
        max_length = temp_config.get('variable_length', {}).get('max_length', 10000)

        if prefix_length > max_length * 0.8:  # 固定前缀不应超过最大长度的80%
            return False, (f"固定前缀长度 {prefix_length:,} 过长，"
                          f"建议不超过最大序列长度的80% ({max_length * 0.8:,.0f})")

        return True, ""

    except Exception as e:
        return False, f"兼容性验证失败: {e}"
