#!/usr/bin/env python3
"""
特征提取模块：集成统计特征和LSTM时序特征

该模块负责：
1. 从温度序列中提取统计特征
2. 使用LSTM提取时序特征
3. 特征融合和标准化
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from scipy import stats
from scipy.signal import welch
from sklearn.preprocessing import StandardScaler
from typing import Dict, List, Tuple, Optional, Union
import logging
import yaml
import warnings

logger = logging.getLogger(__name__)

# GPU设备检测和配置
def get_device():
    """
    检测并返回最佳可用设备

    Returns:
        torch.device: 可用的设备 (cuda/cpu)
    """
    if torch.cuda.is_available():
        device = torch.device('cuda')
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"检测到GPU: {gpu_name}, 显存: {gpu_memory:.1f}GB")
        logger.info(f"CUDA版本: {torch.version.cuda}")
        return device
    else:
        logger.warning("未检测到CUDA GPU，将使用CPU")
        return torch.device('cpu')

# 全局设备配置
DEVICE = get_device()


class LSTMFeatureExtractor(nn.Module):
    """LSTM时序特征提取器 (GPU加速版本)"""

    def __init__(self, input_size: int = 1, hidden_size: int = 64,
                 num_layers: int = 2, dropout: float = 0.2, device: torch.device = None):
        """
        初始化LSTM特征提取器

        Args:
            input_size: 输入特征维度
            hidden_size: 隐藏层大小
            num_layers: LSTM层数
            dropout: Dropout率
            device: 计算设备
        """
        super(LSTMFeatureExtractor, self).__init__()

        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.device = device if device is not None else DEVICE

        # LSTM层
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            batch_first=True
        )

        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_size,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )

        # 输出层
        self.fc = nn.Linear(hidden_size, hidden_size)
        self.dropout = nn.Dropout(dropout)

        # 移动模型到指定设备
        self.to(self.device)
        logger.info(f"LSTM特征提取器已移动到设备: {self.device}")

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播 (GPU加速)

        Args:
            x: 输入序列 [batch_size, seq_len, input_size]

        Returns:
            特征向量 [batch_size, hidden_size]
        """
        # 确保输入在正确的设备上
        x = x.to(self.device)

        # LSTM处理
        lstm_out, (hidden, cell) = self.lstm(x)

        # 注意力机制
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)

        # 全局平均池化
        features = torch.mean(attn_out, dim=1)

        # 输出层
        features = self.dropout(features)
        features = self.fc(features)

        return features

    def ensure_device_compatibility(self):
        """
        确保设备兼容性（用于旧模型的向后兼容，增强版）
        """
        # 1. 确保有device属性
        if not hasattr(self, 'device'):
            self.device = torch.device('cpu')
            logger.info("为旧版本LSTM模型添加CPU设备属性")

        # 2. 确保有其他必要属性
        if not hasattr(self, 'hidden_size'):
            self.hidden_size = 64  # 默认值
            logger.info("为旧版本LSTM模型添加hidden_size属性")

        if not hasattr(self, 'num_layers'):
            self.num_layers = 2  # 默认值
            logger.info("为旧版本LSTM模型添加num_layers属性")

        # 3. 确保模型在正确的设备上
        try:
            current_device = next(self.parameters()).device
            if self.device != current_device:
                self.to(self.device)
                logger.info(f"LSTM模型已移动到设备: {self.device}")
        except Exception as e:
            logger.warning(f"移动LSTM模型到设备时出错: {e}")
            # 如果出错，强制使用CPU
            self.device = torch.device('cpu')
            try:
                self.to(self.device)
                logger.info("强制使用CPU设备")
            except Exception as e2:
                logger.error(f"强制移动到CPU也失败: {e2}")
                # 最后的兜底方案：不移动模型，只设置device属性
                pass

    def get_device_info(self) -> Dict[str, str]:
        """
        获取设备信息

        Returns:
            设备信息字典
        """
        info = {
            'device': str(self.device),
            'device_type': self.device.type
        }

        if self.device.type == 'cuda':
            info.update({
                'gpu_name': torch.cuda.get_device_name(self.device),
                'gpu_memory_total': f"{torch.cuda.get_device_properties(self.device).total_memory / 1024**3:.1f}GB",
                'gpu_memory_allocated': f"{torch.cuda.memory_allocated(self.device) / 1024**3:.1f}GB",
                'gpu_memory_cached': f"{torch.cuda.memory_reserved(self.device) / 1024**3:.1f}GB"
            })

        return info


class StatisticalFeatureExtractor:
    """统计特征提取器"""
    
    def __init__(self):
        """初始化统计特征提取器"""
        self.feature_names = []
    
    def extract_basic_statistics(self, sequence: np.ndarray) -> Dict[str, float]:
        """提取基础统计特征"""
        features = {}
        
        # 基本统计量
        features['mean'] = np.mean(sequence)
        features['std'] = np.std(sequence)
        features['var'] = np.var(sequence)
        features['min'] = np.min(sequence)
        features['max'] = np.max(sequence)
        features['range'] = features['max'] - features['min']
        features['median'] = np.median(sequence)
        
        # 分位数
        features['q25'] = np.percentile(sequence, 25)
        features['q75'] = np.percentile(sequence, 75)
        features['q90'] = np.percentile(sequence, 90)
        features['q95'] = np.percentile(sequence, 95)
        features['iqr'] = features['q75'] - features['q25']
        
        # 分布形状特征
        features['skewness'] = stats.skew(sequence)
        features['kurtosis'] = stats.kurtosis(sequence)
        
        # 变异系数
        features['cv'] = features['std'] / features['mean'] if features['mean'] != 0 else 0
        
        return features
    
    def extract_trend_features(self, sequence: np.ndarray) -> Dict[str, float]:
        """提取趋势特征"""
        features = {}
        
        # 线性趋势
        x = np.arange(len(sequence))
        slope, intercept, r_value, p_value, std_err = stats.linregress(x, sequence)
        
        features['trend_slope'] = slope
        features['trend_intercept'] = intercept
        features['trend_r_squared'] = r_value ** 2
        features['trend_p_value'] = p_value
        features['trend_std_err'] = std_err
        features['trend_strength'] = abs(slope)
        
        # 单调性检测
        diff_sequence = np.diff(sequence)
        features['monotonic_increasing'] = np.sum(diff_sequence > 0) / len(diff_sequence)
        features['monotonic_decreasing'] = np.sum(diff_sequence < 0) / len(diff_sequence)
        
        return features
    
    def extract_variability_features(self, sequence: np.ndarray) -> Dict[str, float]:
        """提取变化性特征"""
        features = {}
        
        # 一阶差分统计
        diff1 = np.diff(sequence)
        features['diff1_mean'] = np.mean(diff1)
        features['diff1_std'] = np.std(diff1)
        features['diff1_var'] = np.var(diff1)
        features['diff1_max'] = np.max(np.abs(diff1))
        
        # 二阶差分统计
        diff2 = np.diff(sequence, n=2)
        features['diff2_mean'] = np.mean(diff2)
        features['diff2_std'] = np.std(diff2)
        features['diff2_var'] = np.var(diff2)
        
        # 变化率特征
        features['change_rate'] = (sequence[-1] - sequence[0]) / len(sequence)
        
        # 波动性指标
        rolling_std = pd.Series(sequence).rolling(window=min(100, len(sequence)//10)).std()
        features['volatility_mean'] = np.nanmean(rolling_std)
        features['volatility_std'] = np.nanstd(rolling_std)
        
        # 零交叉率
        mean_centered = sequence - np.mean(sequence)
        zero_crossings = np.sum(np.diff(np.sign(mean_centered)) != 0)
        features['zero_crossing_rate'] = zero_crossings / len(sequence)
        
        return features
    
    def extract_autocorr_features(self, sequence: np.ndarray, max_lags: int = 50) -> Dict[str, float]:
        """提取自相关特征（优化版本）"""
        features = {}

        # 限制最大滞后数和序列长度以提高性能
        max_lags = min(max_lags, len(sequence) // 4, 100)  # 最多100个滞后

        # 如果序列太长，进行下采样以提高性能
        if len(sequence) > 2000:
            # 下采样到2000个点
            indices = np.linspace(0, len(sequence) - 1, 2000, dtype=int)
            sequence = sequence[indices]
            max_lags = min(max_lags, 50)  # 进一步限制滞后数

        if max_lags > 1 and len(sequence) > 10:
            try:
                # 使用更高效的自相关计算方法
                mean_centered = sequence - np.mean(sequence)

                # 只计算需要的滞后值，而不是完整的自相关函数
                target_lags = [1, 5, 10, 20]
                target_lags = [lag for lag in target_lags if lag < len(sequence) and lag <= max_lags]

                for lag in target_lags:
                    if lag < len(mean_centered):
                        # 直接计算特定滞后的自相关
                        autocorr_val = np.corrcoef(mean_centered[:-lag], mean_centered[lag:])[0, 1]
                        features[f'autocorr_lag_{lag}'] = autocorr_val if not np.isnan(autocorr_val) else 0
                    else:
                        features[f'autocorr_lag_{lag}'] = 0

                # 填充缺失的滞后值
                for lag in [1, 5, 10, 20]:
                    if f'autocorr_lag_{lag}' not in features:
                        features[f'autocorr_lag_{lag}'] = 0

                # 计算自相关衰减（使用前几个可用的滞后值）
                available_autocorr = [features[f'autocorr_lag_{lag}'] for lag in target_lags[:5]]
                features['autocorr_decay'] = np.mean(available_autocorr) if available_autocorr else 0

            except Exception as e:
                logger.warning(f"自相关计算失败，使用默认值: {e}")
                # 如果计算失败，设置默认值
                for lag in [1, 5, 10, 20]:
                    features[f'autocorr_lag_{lag}'] = 0
                features['autocorr_decay'] = 0
        else:
            # 序列太短，设置默认值
            for lag in [1, 5, 10, 20]:
                features[f'autocorr_lag_{lag}'] = 0
            features['autocorr_decay'] = 0

        return features

    def extract_frequency_features(self, sequence: np.ndarray, fs: float = 1.0) -> Dict[str, float]:
        """提取频域特征"""
        features = {}

        try:
            # 计算功率谱密度
            freqs, psd = welch(sequence, fs=fs, nperseg=min(256, len(sequence)//4))

            # 总功率
            features['total_power'] = np.sum(psd)

            # 主频率
            dominant_freq_idx = np.argmax(psd)
            features['dominant_frequency'] = freqs[dominant_freq_idx]
            features['dominant_power'] = psd[dominant_freq_idx]

            # 频率重心
            features['spectral_centroid'] = np.sum(freqs * psd) / np.sum(psd)

            # 频谱熵
            psd_norm = psd / np.sum(psd)
            psd_norm = psd_norm[psd_norm > 0]
            features['spectral_entropy'] = -np.sum(psd_norm * np.log2(psd_norm))

        except Exception as e:
            logger.warning(f"频域特征提取失败: {e}")
            features.update({
                'total_power': 0, 'dominant_frequency': 0, 'dominant_power': 0,
                'spectral_centroid': 0, 'spectral_entropy': 0
            })

        return features
    
    def extract_all_features(self, sequence: np.ndarray, fast_mode: bool = False) -> Dict[str, float]:
        """提取所有统计特征（带性能监控和快速模式）"""
        import time

        all_features = {}

        # 序列长度特征
        all_features['sequence_length'] = len(sequence)

        # 快速模式：只提取最基础的特征
        if fast_mode:
            # 只计算最基本的统计量，避免复杂计算
            all_features.update({
                'mean': float(np.mean(sequence)),
                'std': float(np.std(sequence)),
                'min': float(np.min(sequence)),
                'max': float(np.max(sequence)),
                'median': float(np.median(sequence)),
                'range': float(np.max(sequence) - np.min(sequence)),
                'q25': float(np.percentile(sequence, 25)),
                'q75': float(np.percentile(sequence, 75)),
                'iqr': float(np.percentile(sequence, 75) - np.percentile(sequence, 25)),
                'skewness': 0.0,  # 跳过复杂计算
                'kurtosis': 0.0,  # 跳过复杂计算
                'cv': float(np.std(sequence) / (np.mean(sequence) + 1e-8)),
                'rms': float(np.sqrt(np.mean(sequence**2))),
                'mad': 0.0,  # 跳过复杂计算
                'energy': float(np.sum(sequence**2)),
                'power': float(np.mean(sequence**2)),
                'zero_crossing_rate': 0.0,  # 跳过复杂计算
                # 趋势特征（简化）
                'slope': 0.0,  # 跳过复杂计算
                'intercept': float(np.mean(sequence)),
                'r_squared': 0.0,  # 跳过复杂计算
                'trend_strength': 0.0,  # 跳过复杂计算
                # 变异性特征（简化）
                'variance': float(np.var(sequence)),
                'std_normalized': float(np.std(sequence) / (np.mean(sequence) + 1e-8)),
                'range_normalized': float((np.max(sequence) - np.min(sequence)) / (np.mean(sequence) + 1e-8)),
                'percentile_range': float(np.percentile(sequence, 90) - np.percentile(sequence, 10)),
                'robust_std': 0.0,  # 跳过复杂计算
            })

            # 自相关特征占位符
            for lag in [1, 5, 10, 20]:
                all_features[f'autocorr_lag_{lag}'] = 0.0
            all_features['autocorr_decay'] = 0.0

            # 频域特征占位符
            all_features.update({
                'total_power': 0.0, 'dominant_frequency': 0.0, 'dominant_power': 0.0,
                'spectral_centroid': 0.0, 'spectral_entropy': 0.0
            })

            return all_features

        # 正常模式：提取所有特征（带时间监控）
        start_time = time.time()
        all_features.update(self.extract_basic_statistics(sequence))
        basic_time = time.time() - start_time

        start_time = time.time()
        all_features.update(self.extract_trend_features(sequence))
        trend_time = time.time() - start_time

        start_time = time.time()
        all_features.update(self.extract_variability_features(sequence))
        var_time = time.time() - start_time

        start_time = time.time()
        all_features.update(self.extract_autocorr_features(sequence))
        autocorr_time = time.time() - start_time

        start_time = time.time()
        all_features.update(self.extract_frequency_features(sequence))
        freq_time = time.time() - start_time

        # 如果自相关计算时间过长，记录警告
        if autocorr_time > 1.0:  # 超过1秒
            logger.warning(f"自相关特征计算耗时过长: {autocorr_time:.2f}秒 (序列长度: {len(sequence)})")

        # 记录详细时间（仅在调试模式下）
        total_time = basic_time + trend_time + var_time + autocorr_time + freq_time
        if total_time > 2.0:  # 总时间超过2秒
            logger.info(f"特征提取时间分布 - 基础:{basic_time:.2f}s, 趋势:{trend_time:.2f}s, "
                       f"变异:{var_time:.2f}s, 自相关:{autocorr_time:.2f}s, 频域:{freq_time:.2f}s")

        return all_features


class FeatureExtractor:
    """综合特征提取器：统计特征 + LSTM时序特征 (GPU加速版本)"""

    def __init__(self, config_path: str = "config/config.yaml", device: torch.device = None):
        """
        初始化特征提取器

        Args:
            config_path: 配置文件路径
            device: 计算设备
        """
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)

        self.feature_config = self.config['feature_extraction']
        self.device = device if device is not None else DEVICE

        # 初始化子模块
        self.statistical_extractor = StatisticalFeatureExtractor()

        # LSTM特征提取器 (GPU加速)
        lstm_config = self.feature_config['lstm_features']
        self.lstm_extractor = LSTMFeatureExtractor(
            input_size=1,
            hidden_size=lstm_config['hidden_size'],
            num_layers=lstm_config['num_layers'],
            dropout=lstm_config['dropout'],
            device=self.device
        )

        # 变长序列支持配置（确保属性始终存在）
        try:
            self.variable_length_enabled = self.config.get('pso', {}).get('temperature_sequence', {}).get('variable_length', {}).get('enable', False)
        except Exception as e:
            logger.warning(f"读取变长序列配置失败: {e}，使用默认值False")
            self.variable_length_enabled = False

        if self.variable_length_enabled:
            self.max_sequence_length = lstm_config.get('max_sequence_length', 10000)
            logger.info(f"特征提取器启用变长序列支持，最大长度: {self.max_sequence_length}")
        else:
            self.max_sequence_length = 10000  # 设置默认值
            logger.info("特征提取器使用固定长度模式")

        # 标准化器
        self.scaler = StandardScaler()
        self.is_fitted = False
        self.feature_names = []

        # GPU内存管理
        self.enable_gpu_memory_optimization = True
        if self.device.type == 'cuda':
            logger.info(f"特征提取器使用GPU: {torch.cuda.get_device_name(self.device)}")
            # 清理GPU缓存
            torch.cuda.empty_cache()

        # 确保LSTM模型的设备兼容性
        self.ensure_lstm_compatibility()
    
    def prepare_lstm_input(self, sequence: np.ndarray, target_length: int = None,
                          variable_length_mode: bool = False) -> torch.Tensor:
        """
        准备LSTM输入数据 (GPU优化版本，支持变长序列)

        Args:
            sequence: 温度序列
            target_length: 目标序列长度 (变长模式下可为None)
            variable_length_mode: 是否启用变长模式

        Returns:
            LSTM输入张量 (在指定设备上)
        """
        if variable_length_mode:
            # 变长模式：保持原始长度，但限制最大长度以避免内存问题
            max_length = getattr(self, 'max_sequence_length', 10000)  # 默认最大长度
            if len(sequence) > max_length:
                # 智能下采样：保持序列特征
                indices = np.linspace(0, len(sequence) - 1, max_length, dtype=int)
                sequence = sequence[indices]

            # 最小长度保护
            min_length = 10
            if len(sequence) < min_length:
                # 重复填充到最小长度
                repeat_times = min_length // len(sequence) + 1
                sequence = np.tile(sequence, repeat_times)[:min_length]
        else:
            # 固定长度模式：原有逻辑
            if target_length is None:
                target_length = 100  # 默认长度

            if len(sequence) > target_length:
                indices = np.linspace(0, len(sequence) - 1, target_length, dtype=int)
                sequence = sequence[indices]
            elif len(sequence) < target_length:
                # 重复填充
                repeat_times = target_length // len(sequence) + 1
                sequence = np.tile(sequence, repeat_times)[:target_length]

        # 标准化
        sequence_mean = np.mean(sequence)
        sequence_std = np.std(sequence)
        sequence = (sequence - sequence_mean) / (sequence_std + 1e-8)

        # 转换为张量
        tensor = torch.FloatTensor(sequence).unsqueeze(0).unsqueeze(-1)  # [1, seq_len, 1]

        # 移动到指定设备（向后兼容）
        if hasattr(self, 'device'):
            tensor = tensor.to(self.device)

        return tensor

    def extract_sequence_features(self, sequence: np.ndarray) -> np.ndarray:
        """
        提取单个序列的综合特征 (GPU加速版本，向后兼容)

        Args:
            sequence: 温度序列

        Returns:
            特征向量
        """
        features = []

        # 统计特征（支持快速模式）
        if self.feature_config['statistical_features']['enable']:
            # 检查是否启用快速模式
            fast_mode = self.feature_config['statistical_features'].get('fast_mode', False)
            stat_features = self.statistical_extractor.extract_all_features(sequence, fast_mode=fast_mode)
            features.extend(list(stat_features.values()))

        # LSTM时序特征 (GPU加速，增强向后兼容)
        if self.feature_config['lstm_features']['enable']:
            try:
                # 全面的向后兼容性检查和修复
                self._ensure_full_compatibility()

                # 根据模式准备LSTM输入（确保属性存在）
                variable_length_enabled = getattr(self, 'variable_length_enabled', False)
                if variable_length_enabled:
                    lstm_input = self.prepare_lstm_input(
                        sequence,
                        target_length=None,
                        variable_length_mode=True
                    )
                else:
                    lstm_input = self.prepare_lstm_input(
                        sequence,
                        self.feature_config['lstm_features']['sequence_length'],
                        variable_length_mode=False
                    )

                with torch.no_grad():
                    # 确保模型在评估模式
                    self.lstm_extractor.eval()

                    # GPU推理
                    lstm_features = self.lstm_extractor(lstm_input)

                    # 将结果移回CPU并转换为numpy
                    lstm_features_cpu = lstm_features.squeeze().cpu().numpy()
                    features.extend(lstm_features_cpu.tolist())

                    # GPU内存优化（如果启用）
                    if (hasattr(self, 'enable_gpu_memory_optimization') and
                        self.enable_gpu_memory_optimization and
                        hasattr(self, 'device') and
                        self.device.type == 'cuda'):
                        del lstm_input, lstm_features
                        torch.cuda.empty_cache()

            except Exception as e:
                logger.warning(f"LSTM特征提取失败，使用零向量替代: {e}")
                # 如果GPU推理失败，使用零向量
                hidden_size = self.feature_config['lstm_features']['hidden_size']
                features.extend([0.0] * hidden_size)

        return np.array(features)

    def extract_pairwise_features(self, sequence1: np.ndarray, sequence2: np.ndarray) -> np.ndarray:
        """
        提取成对序列的特征

        Args:
            sequence1: 第一个序列
            sequence2: 第二个序列

        Returns:
            成对特征向量
        """
        # 提取各自的特征
        features1 = self.extract_sequence_features(sequence1)
        features2 = self.extract_sequence_features(sequence2)

        # 特征组合方式
        # 1. 直接拼接
        concat_features = np.concatenate([features1, features2])

        # 2. 差值特征
        diff_features = features1 - features2

        # 3. 比值特征（避免除零）
        ratio_features = np.divide(features1, features2 + 1e-8)

        # 4. 统计特征 (支持变长序列，确保属性存在)
        variable_length_enabled = getattr(self, 'variable_length_enabled', False)
        if variable_length_enabled:
            # 变长序列的相关性计算
            correlation = self._calculate_variable_length_correlation(sequence1, sequence2)
        else:
            # 固定长度的相关性计算
            correlation = np.corrcoef(features1, features2)[0, 1] if len(features1) > 1 else 0

        stat_features = np.array([
            np.mean([features1, features2], axis=0).mean(),
            np.std([features1, features2], axis=0).mean(),
            correlation
        ])

        # 组合所有特征
        combined_features = np.concatenate([
            concat_features, diff_features, ratio_features, stat_features
        ])

        return combined_features

    def _calculate_variable_length_correlation(self, seq1: np.ndarray, seq2: np.ndarray) -> float:
        """
        计算变长序列的相关性

        Args:
            seq1: 第一个序列
            seq2: 第二个序列

        Returns:
            相关系数
        """
        try:
            # 如果长度相同，直接计算
            if len(seq1) == len(seq2):
                correlation = np.corrcoef(seq1, seq2)[0, 1]
                return 0.0 if np.isnan(correlation) else correlation

            # 长度不同时，重采样到较短的长度
            min_length = min(len(seq1), len(seq2))
            if min_length < 10:  # 太短无法计算有意义的相关性
                return 0.0

            # 重采样
            if len(seq1) != min_length:
                indices = np.linspace(0, len(seq1) - 1, min_length, dtype=int)
                seq1_resampled = seq1[indices]
            else:
                seq1_resampled = seq1

            if len(seq2) != min_length:
                indices = np.linspace(0, len(seq2) - 1, min_length, dtype=int)
                seq2_resampled = seq2[indices]
            else:
                seq2_resampled = seq2

            # 计算相关性
            correlation = np.corrcoef(seq1_resampled, seq2_resampled)[0, 1]
            return 0.0 if np.isnan(correlation) else correlation

        except Exception as e:
            logger.warning(f"变长序列相关性计算失败: {e}")
            return 0.0

    def fit_transform(self, pairwise_data: List[Dict]) -> Tuple[np.ndarray, List[str]]:
        """
        拟合并转换成对数据为特征矩阵

        Args:
            pairwise_data: 成对比较数据列表

        Returns:
            元组 (特征矩阵, 特征名称列表)
        """
        feature_list = []

        logger.info(f"开始提取 {len(pairwise_data)} 个样本对的特征...")

        for i, pair in enumerate(pairwise_data):
            if i % 50 == 0:
                logger.info(f"处理进度: {i}/{len(pairwise_data)}")

            # 提取成对特征
            features = self.extract_pairwise_features(
                pair['sequence_1'],
                pair['sequence_2']
            )
            feature_list.append(features)

        # 转换为矩阵
        feature_matrix = np.array(feature_list)

        # 处理异常值
        feature_matrix = np.nan_to_num(feature_matrix, nan=0.0, posinf=1e6, neginf=-1e6)

        # 标准化
        if self.feature_config['standardization']['enable']:
            feature_matrix = self.scaler.fit_transform(feature_matrix)
            self.is_fitted = True

        # 生成特征名称
        self.feature_names = self._generate_feature_names(feature_matrix.shape[1])

        logger.info(f"特征提取完成，特征维度: {feature_matrix.shape}")

        return feature_matrix, self.feature_names

    def transform(self, pairwise_data: List[Dict]) -> np.ndarray:
        """
        转换新的成对数据为特征矩阵

        Args:
            pairwise_data: 成对比较数据列表

        Returns:
            标准化的特征矩阵
        """
        if not self.is_fitted:
            raise ValueError("特征提取器尚未拟合，请先调用fit_transform")

        feature_list = []

        for pair in pairwise_data:
            features = self.extract_pairwise_features(
                pair['sequence_1'],
                pair['sequence_2']
            )
            feature_list.append(features)

        # 转换为矩阵
        feature_matrix = np.array(feature_list)
        feature_matrix = np.nan_to_num(feature_matrix, nan=0.0, posinf=1e6, neginf=-1e6)

        # 标准化
        if self.feature_config['standardization']['enable']:
            feature_matrix = self.scaler.transform(feature_matrix)

        return feature_matrix

    def _generate_feature_names(self, num_features: int) -> List[str]:
        """
        生成特征名称列表

        Args:
            num_features: 特征数量

        Returns:
            特征名称列表
        """
        names = []

        # 统计特征名称
        if self.feature_config['statistical_features']['enable']:
            stat_features = self.statistical_extractor.extract_all_features(np.array([1, 2, 3]))
            for name in stat_features.keys():
                names.extend([f"{name}_1", f"{name}_2", f"{name}_diff", f"{name}_ratio"])

        # LSTM特征名称
        if self.feature_config['lstm_features']['enable']:
            hidden_size = self.feature_config['lstm_features']['hidden_size']
            for i in range(hidden_size):
                names.extend([f"lstm_{i}_1", f"lstm_{i}_2", f"lstm_{i}_diff", f"lstm_{i}_ratio"])

        # 统计组合特征
        names.extend(["combined_mean", "combined_std", "correlation"])

        # 如果名称数量不匹配，使用通用名称
        if len(names) != num_features:
            names = [f"feature_{i}" for i in range(num_features)]

        return names

    def _ensure_full_compatibility(self):
        """
        全面的向后兼容性检查和修复（增强版）
        """
        # 1. 确保FeatureExtractor本身的属性
        if not hasattr(self, 'device'):
            self.device = torch.device('cpu')
            logger.info("为旧版本FeatureExtractor添加CPU设备属性")

        if not hasattr(self, 'enable_gpu_memory_optimization'):
            self.enable_gpu_memory_optimization = False
            logger.info("为旧版本FeatureExtractor添加GPU内存优化属性")

        if not hasattr(self, 'variable_length_enabled'):
            self.variable_length_enabled = False
            logger.info("为旧版本FeatureExtractor添加变长序列属性")

        if not hasattr(self, 'max_sequence_length'):
            self.max_sequence_length = 10000
            logger.info("为旧版本FeatureExtractor添加最大序列长度属性")

        # 2. 确保LSTM提取器的属性和兼容性
        if hasattr(self, 'lstm_extractor') and self.lstm_extractor is not None:
            # 检查LSTM提取器是否有device属性
            if not hasattr(self.lstm_extractor, 'device'):
                self.lstm_extractor.device = self.device
                logger.info("为旧版本LSTM提取器添加设备属性")

            # 检查LSTM提取器是否有其他必要属性
            if not hasattr(self.lstm_extractor, 'hidden_size'):
                # 从配置中获取hidden_size
                self.lstm_extractor.hidden_size = self.feature_config['lstm_features']['hidden_size']
                logger.info("为旧版本LSTM提取器添加hidden_size属性")

            if not hasattr(self.lstm_extractor, 'num_layers'):
                self.lstm_extractor.num_layers = self.feature_config['lstm_features']['num_layers']
                logger.info("为旧版本LSTM提取器添加num_layers属性")

            # 确保LSTM模型在正确的设备上
            try:
                # 检查模型参数是否在正确的设备上
                model_device = next(self.lstm_extractor.parameters()).device
                if model_device != self.device:
                    self.lstm_extractor.to(self.device)
                    logger.info(f"LSTM模型已移动到设备: {self.device}")
            except Exception as e:
                logger.warning(f"检查LSTM模型设备时出错: {e}")
                # 强制移动到CPU以确保兼容性
                self.device = torch.device('cpu')
                self.lstm_extractor.device = self.device
                self.lstm_extractor.to(self.device)
                logger.info("强制使用CPU设备以确保兼容性")

            # 调用LSTM的兼容性方法（如果存在）
            if hasattr(self.lstm_extractor, 'ensure_device_compatibility'):
                try:
                    self.lstm_extractor.ensure_device_compatibility()
                except Exception as e:
                    logger.warning(f"调用LSTM兼容性方法失败: {e}")

    def ensure_lstm_compatibility(self):
        """
        确保LSTM模型的设备兼容性（用于旧模型的向后兼容）
        """
        # 首先确保FeatureExtractor本身有设备属性
        if not hasattr(self, 'device'):
            self.device = torch.device('cpu')
            logger.info("为旧版本FeatureExtractor添加CPU设备属性")

        # 确保有GPU内存优化属性
        if not hasattr(self, 'enable_gpu_memory_optimization'):
            self.enable_gpu_memory_optimization = False
            logger.info("为旧版本FeatureExtractor添加GPU内存优化属性")

        if hasattr(self, 'lstm_extractor') and self.lstm_extractor is not None:
            # 确保LSTM提取器有设备属性
            if not hasattr(self.lstm_extractor, 'device'):
                self.lstm_extractor.device = self.device
                logger.info("为旧版本LSTM提取器添加设备属性")

            # 调用LSTM的兼容性方法
            if hasattr(self.lstm_extractor, 'ensure_device_compatibility'):
                self.lstm_extractor.ensure_device_compatibility()
            else:
                # 如果没有兼容性方法，手动设置设备
                self.lstm_extractor.device = self.device
                self.lstm_extractor.to(self.device)
                logger.info(f"手动设置LSTM提取器设备: {self.device}")

    def get_gpu_memory_info(self) -> Dict[str, str]:
        """
        获取GPU内存使用信息

        Returns:
            GPU内存信息字典
        """
        if self.device.type == 'cuda':
            return {
                'device': str(self.device),
                'memory_allocated': f"{torch.cuda.memory_allocated(self.device) / 1024**3:.2f}GB",
                'memory_reserved': f"{torch.cuda.memory_reserved(self.device) / 1024**3:.2f}GB",
                'memory_total': f"{torch.cuda.get_device_properties(self.device).total_memory / 1024**3:.2f}GB"
            }
        else:
            return {'device': 'CPU', 'memory_info': 'N/A'}

    def clear_gpu_cache(self):
        """清理GPU缓存"""
        if self.device.type == 'cuda':
            torch.cuda.empty_cache()
            logger.info("GPU缓存已清理")

    def optimize_for_inference(self):
        """优化模型以进行推理"""
        if hasattr(self, 'lstm_extractor'):
            self.lstm_extractor.eval()
            # 启用推理优化
            with torch.no_grad():
                if self.device.type == 'cuda':
                    # 预热GPU
                    dummy_input = torch.randn(1, 100, 1).to(self.device)
                    _ = self.lstm_extractor(dummy_input)
                    del dummy_input
                    torch.cuda.empty_cache()
            logger.info("模型已优化用于推理")


def main():
    """测试特征提取功能"""
    # 创建测试数据
    test_sequence1 = np.random.randn(1000) + 20
    test_sequence2 = np.sin(np.linspace(0, 10*np.pi, 500)) + 25

    # 创建成对数据
    pairwise_data = [{
        'sequence_1': test_sequence1,
        'sequence_2': test_sequence2,
        'label': 1
    }]

    # 测试特征提取
    extractor = FeatureExtractor()
    features, feature_names = extractor.fit_transform(pairwise_data)

    print(f"提取的特征数量: {len(feature_names)}")
    print(f"特征矩阵形状: {features.shape}")
    print(f"前10个特征名称: {feature_names[:10]}")


if __name__ == "__main__":
    main()
