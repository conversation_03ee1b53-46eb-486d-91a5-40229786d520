#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sample_1.xlsx 详细数据分析脚本
专门分析酯化反应数据
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def detailed_analysis():
    """详细分析Sample_1.xlsx数据"""
    
    excel_file = "data/Esterification/Sample_1.xlsx"
    
    try:
        # 读取数据
        print(f"正在读取文件: {excel_file}")
        df = pd.read_excel(excel_file, sheet_name=0)
        
        # 清理列名
        df.columns = [col.strip().replace('\t', '') for col in df.columns]
        
        # 转换为数值类型
        for col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 获取数据列（假设是温度或浓度数据）
        data_col = df.columns[0]
        data = df[data_col].dropna()
        
        print(f"\n=== 数据基本信息 ===")
        print(f"数据列名: {data_col}")
        print(f"数据点数: {len(data)}")
        print(f"数据范围: {data.min():.3f} - {data.max():.3f}")
        print(f"均值: {data.mean():.3f}")
        print(f"标准差: {data.std():.3f}")
        print(f"中位数: {data.median():.3f}")
        
        # 创建综合分析图
        fig = plt.figure(figsize=(16, 12))
        
        # 1. 时间序列图
        ax1 = plt.subplot(2, 3, 1)
        time_points = np.arange(len(data))
        plt.plot(time_points, data, 'b-', linewidth=1, alpha=0.8)
        plt.title(f'{data_col} 随时间变化', fontsize=12, fontweight='bold')
        plt.xlabel('时间点')
        plt.ylabel(f'{data_col}')
        plt.grid(True, alpha=0.3)
        
        # 添加趋势线
        z = np.polyfit(time_points, data, 1)
        p = np.poly1d(z)
        plt.plot(time_points, p(time_points), "r--", alpha=0.8, linewidth=2, label=f'趋势线 (斜率: {z[0]:.6f})')
        plt.legend()
        
        # 2. 数据分布直方图
        ax2 = plt.subplot(2, 3, 2)
        plt.hist(data, bins=50, alpha=0.7, color='skyblue', edgecolor='black', density=True)
        plt.title(f'{data_col} 分布直方图', fontsize=12, fontweight='bold')
        plt.xlabel(f'{data_col}')
        plt.ylabel('密度')
        plt.grid(True, alpha=0.3)
        
        # 添加正态分布拟合
        mu, sigma = stats.norm.fit(data)
        x = np.linspace(data.min(), data.max(), 100)
        plt.plot(x, stats.norm.pdf(x, mu, sigma), 'r-', linewidth=2, label=f'正态拟合 (μ={mu:.2f}, σ={sigma:.2f})')
        plt.legend()
        
        # 3. 箱线图
        ax3 = plt.subplot(2, 3, 3)
        plt.boxplot(data, vert=True)
        plt.title(f'{data_col} 箱线图', fontsize=12, fontweight='bold')
        plt.ylabel(f'{data_col}')
        plt.grid(True, alpha=0.3)
        
        # 添加统计信息
        q1 = np.percentile(data, 25)
        q3 = np.percentile(data, 75)
        iqr = q3 - q1
        stats_text = f'Q1: {q1:.2f}\nQ3: {q3:.2f}\nIQR: {iqr:.2f}'
        plt.text(1.1, q1, stats_text, fontsize=10, bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        # 4. 移动平均
        ax4 = plt.subplot(2, 3, 4)
        window_sizes = [100, 500, 1000]
        colors = ['red', 'green', 'orange']
        
        plt.plot(time_points, data, 'b-', linewidth=0.5, alpha=0.5, label='原始数据')
        
        for window, color in zip(window_sizes, colors):
            if len(data) > window:
                moving_avg = data.rolling(window=window, center=True).mean()
                plt.plot(time_points, moving_avg, color=color, linewidth=2, label=f'{window}点移动平均')
        
        plt.title('移动平均分析', fontsize=12, fontweight='bold')
        plt.xlabel('时间点')
        plt.ylabel(f'{data_col}')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 5. 变化率分析
        ax5 = plt.subplot(2, 3, 5)
        diff_data = np.diff(data)
        plt.plot(time_points[1:], diff_data, 'purple', linewidth=1, alpha=0.8)
        plt.title('数据变化率', fontsize=12, fontweight='bold')
        plt.xlabel('时间点')
        plt.ylabel(f'{data_col} 变化率')
        plt.grid(True, alpha=0.3)
        plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # 添加变化率统计
        change_stats = f'变化率统计:\n均值: {np.mean(diff_data):.6f}\n标准差: {np.std(diff_data):.6f}'
        plt.text(0.02, 0.98, change_stats, transform=ax5.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        # 6. 累积分布函数
        ax6 = plt.subplot(2, 3, 6)
        sorted_data = np.sort(data)
        cumulative = np.arange(1, len(sorted_data) + 1) / len(sorted_data)
        plt.plot(sorted_data, cumulative, 'b-', linewidth=2)
        plt.title('累积分布函数 (CDF)', fontsize=12, fontweight='bold')
        plt.xlabel(f'{data_col}')
        plt.ylabel('累积概率')
        plt.grid(True, alpha=0.3)
        
        # 添加百分位数标记
        percentiles = [25, 50, 75, 90, 95]
        for p in percentiles:
            val = np.percentile(data, p)
            plt.axvline(x=val, color='red', linestyle='--', alpha=0.7)
            plt.text(val, p/100, f'{p}%', rotation=90, fontsize=9)
        
        # 调整布局
        plt.suptitle(f'Sample_1.xlsx 详细数据分析 - {data_col}', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        # 保存图片
        output_file = "results/sample1_detailed_analysis.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"\n详细分析图已保存至: {output_file}")
        
        # 显示图片
        plt.show()
        
        # 打印详细统计报告
        print_detailed_stats(data, data_col)
        
        return data
        
    except Exception as e:
        print(f"分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def print_detailed_stats(data, col_name):
    """打印详细统计报告"""
    
    print(f"\n=== {col_name} 详细统计报告 ===")
    
    # 基本统计
    print(f"\n【基本统计】")
    print(f"数据点数: {len(data):,}")
    print(f"均值: {data.mean():.6f}")
    print(f"标准差: {data.std():.6f}")
    print(f"方差: {data.var():.6f}")
    print(f"最小值: {data.min():.6f}")
    print(f"最大值: {data.max():.6f}")
    print(f"范围: {data.max() - data.min():.6f}")
    print(f"中位数: {data.median():.6f}")
    
    # 百分位数
    print(f"\n【百分位数】")
    percentiles = [5, 10, 25, 50, 75, 90, 95, 99]
    for p in percentiles:
        val = np.percentile(data, p)
        print(f"{p:2d}%: {val:.6f}")
    
    # 偏度和峰度
    print(f"\n【分布特征】")
    skewness = stats.skew(data)
    kurtosis = stats.kurtosis(data)
    print(f"偏度 (Skewness): {skewness:.6f}")
    print(f"峰度 (Kurtosis): {kurtosis:.6f}")
    
    # 正态性检验
    print(f"\n【正态性检验】")
    try:
        # Shapiro-Wilk检验（适用于小样本）
        if len(data) <= 5000:
            stat, p_value = stats.shapiro(data[:5000])  # 限制样本大小
            print(f"Shapiro-Wilk检验: 统计量={stat:.6f}, p值={p_value:.6f}")
        
        # Kolmogorov-Smirnov检验
        stat, p_value = stats.kstest(data, 'norm', args=(data.mean(), data.std()))
        print(f"K-S检验: 统计量={stat:.6f}, p值={p_value:.6f}")
    except Exception as e:
        print(f"正态性检验失败: {e}")
    
    # 变化趋势
    print(f"\n【变化趋势】")
    diff_data = np.diff(data)
    print(f"平均变化率: {np.mean(diff_data):.6f}")
    print(f"变化率标准差: {np.std(diff_data):.6f}")
    print(f"最大增幅: {np.max(diff_data):.6f}")
    print(f"最大降幅: {np.min(diff_data):.6f}")
    
    # 线性趋势
    time_points = np.arange(len(data))
    slope, intercept, r_value, p_value, std_err = stats.linregress(time_points, data)
    print(f"\n【线性趋势】")
    print(f"斜率: {slope:.6f}")
    print(f"截距: {intercept:.6f}")
    print(f"相关系数 R: {r_value:.6f}")
    print(f"决定系数 R²: {r_value**2:.6f}")
    print(f"p值: {p_value:.6f}")

if __name__ == "__main__":
    # 运行详细分析
    data = detailed_analysis()
