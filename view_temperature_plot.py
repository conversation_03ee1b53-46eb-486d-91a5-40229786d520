#!/usr/bin/env python3
"""
查看生成的温度序列对比图表
"""

import os
import glob
from PIL import Image
import matplotlib.pyplot as plt

def view_latest_plot():
    """查看最新生成的温度序列对比图表"""
    
    # 查找最新的对比图表
    plot_files = glob.glob("results/temperature_sequences_comparison_*.png")
    
    if not plot_files:
        print("未找到温度序列对比图表")
        return
    
    # 获取最新的文件
    latest_plot = max(plot_files, key=os.path.getctime)
    
    print(f"显示图表: {latest_plot}")
    
    try:
        # 使用PIL打开图片
        img = Image.open(latest_plot)
        
        # 使用matplotlib显示
        plt.figure(figsize=(16, 12))
        plt.imshow(img)
        plt.axis('off')
        plt.title('温度序列对比图表', fontsize=16, pad=20)
        plt.tight_layout()
        plt.show()
        
        print(f"图表文件大小: {os.path.getsize(latest_plot) / 1024:.1f} KB")
        print(f"图片尺寸: {img.size}")
        
    except Exception as e:
        print(f"显示图表时出错: {e}")
        print(f"请手动打开文件: {latest_plot}")

if __name__ == "__main__":
    view_latest_plot()
